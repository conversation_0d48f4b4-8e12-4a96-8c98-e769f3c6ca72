# OpenHands 部署项目

这是一个生产级的OpenHands部署项目，包含完整的备份、恢复和部署脚本。

## 🚀 快速开始

### 当前部署状态
- ✅ OpenHands 0.51 版本
- ✅ DeepSeek API 集成
- ✅ 中文界面
- ✅ 沙盒环境正常
- ✅ 自动备份和恢复

### 访问地址
- 网站: https://ai.guiyunai.fun
- 设置: https://ai.guiyunai.fun/settings

## 📁 项目结构

```
/www/wwwroot/ai.guiyunai.fun/
├── scripts/
│   ├── deploy.sh          # 部署脚本
│   └── backup.sh          # 备份脚本
├── config-backup/
│   └── settings-template.json  # 配置模板
├── backups/               # 备份文件目录
├── OpenHands/            # OpenHands 源码
└── README.md             # 本文档
```

## 🛠️ 常用命令

### 部署管理
```bash
# 完整部署
./scripts/deploy.sh deploy

# 重启服务
./scripts/deploy.sh restart

# 停止服务
./scripts/deploy.sh stop

# 检查状态
./scripts/deploy.sh status
```

### 备份管理
```bash
# 创建备份
./scripts/backup.sh

# 恢复到指定备份
./backups/openhands_backup_YYYYMMDD_HHMMSS_restore.sh
```

### 手动Docker命令
```bash
# 标准部署命令
docker run -it --rm --pull=never \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik \
    -e LOG_ALL_EVENTS=true \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v ~/.openhands:/.openhands \
    -p 3000:3000 \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app \
    docker.all-hands.dev/all-hands-ai/openhands:0.51
```

## ⚙️ 配置说明

### 主要配置文件
- `~/.openhands/settings.json` - 主配置文件
- `config-backup/settings-template.json` - 配置模板

### 关键配置项
```json
{
  "language": "zh-CN",
  "agent": "CodeActAgent",
  "llm_model": "deepseek/deepseek-chat",
  "llm_base_url": "https://api.deepseek.com/v1",
  "sandbox_runtime_container_image": "docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik"
}
```

## 🔄 备份和恢复

### 自动备份
备份脚本会自动备份：
- 配置文件（移除敏感信息）
- 会话数据
- Docker信息
- 创建恢复脚本

### 恢复流程
1. 选择备份点
2. 运行恢复脚本
3. 配置API密钥
4. 验证服务状态

## 🚨 故障排除

### 常见问题

1. **沙盒无法启动**
   ```bash
   # 检查Docker服务
   systemctl status docker
   
   # 重启服务
   ./scripts/deploy.sh restart
   ```

2. **API密钥问题**
   ```bash
   # 检查配置
   cat ~/.openhands/settings.json | grep llm_api_key
   
   # 重新配置
   cp config-backup/settings-template.json ~/.openhands/settings.json
   # 编辑文件添加API密钥
   ```

3. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep 3000
   
   # 停止冲突服务
   ./scripts/deploy.sh stop
   ```

### 日志查看
```bash
# 查看容器日志
docker logs openhands-app

# 实时日志
docker logs -f openhands-app
```

## 🔐 安全注意事项

1. **API密钥安全**
   - 不要将包含API密钥的文件提交到Git
   - 定期轮换API密钥
   - 使用环境变量存储敏感信息

2. **网络安全**
   - 确保防火墙配置正确
   - 使用HTTPS访问
   - 定期更新Docker镜像

3. **备份安全**
   - 备份文件不包含敏感信息
   - 定期清理旧备份
   - 加密重要备份

## 📞 支持

如果遇到问题：
1. 查看本文档的故障排除部分
2. 检查Docker和系统日志
3. 参考OpenHands官方文档
4. 使用备份恢复到已知工作状态

## 📝 更新日志

- **2025-08-01**: 初始部署，配置DeepSeek API，创建备份系统
- 系统审计和清理，优化性能
- 创建完整的部署和恢复脚本

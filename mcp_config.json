{"sse_servers": ["http://host.docker.internal:8080/sse", "http://host.docker.internal:8081/sse", "http://host.docker.internal:8082/sse", "http://host.docker.internal:8084/sse", "http://host.docker.internal:8090/sse", "http://host.docker.internal:8100/sse", "http://host.docker.internal:8101/sse", "http://host.docker.internal:8110/sse"], "stdio_servers": [{"name": "feng<PERSON>i", "command": "python3", "args": ["/workspace/mcp_servers/fengshui_server.py"]}, {"name": "yijing", "command": "python3", "args": ["/workspace/mcp_servers/yijing_server.py"]}]}
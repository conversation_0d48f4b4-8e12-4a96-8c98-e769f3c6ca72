#!/bin/bash

echo "🎯 OpenHands最终部署脚本"
echo "================================"

echo "🔧 修复权限问题并重新部署..."

# 创建OpenHands配置目录
mkdir -p /www/wwwroot/ai.guiyunai.fun/.openhands
chmod 755 /www/wwwroot/ai.guiyunai.fun/.openhands

echo "✅ 配置目录已创建"

echo ""
echo "🚀 启动OpenHands容器（修复权限）..."

# 重新启动容器，映射配置目录解决权限问题
docker run -d \
    --name openhands-app \
    --restart unless-stopped \
    -p 3000:3000 \
    -v /www/wwwroot/ai.guiyunai.fun/workspace:/workspace \
    -v /www/wwwroot/ai.guiyunai.fun/.openhands:/.openhands \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -e SANDBOX_USER_ID=1000 \
    -e LLM_MODEL="groq/llama-3.3-70b-versatile" \
    -e LLM_API_KEY="********************************************************" \
    -e LLM_BASE_URL="https://api.groq.com/openai/v1" \
    -e WORKSPACE_BASE="/workspace" \
    -e RUNTIME="docker" \
    docker.all-hands.dev/all-hands-ai/openhands:0.51

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功"
else
    echo "❌ 容器启动失败"
    exit 1
fi

echo ""
echo "⏳ 等待服务启动（2分钟）..."

for i in {1..12}; do
    echo "检查 $i/12 (每10秒)..."
    
    # 检查容器状态
    if ! docker ps | grep openhands-app >/dev/null; then
        echo "❌ 容器已停止，查看日志:"
        docker logs openhands-app --tail 20
        exit 1
    fi
    
    # 检查HTTP响应
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ 服务启动成功！"
        break
    elif [ "$HTTP_CODE" = "000" ]; then
        echo "⏳ 连接中..."
    else
        echo "⚠️ HTTP响应: $HTTP_CODE"
    fi
    
    if [ $i -eq 12 ]; then
        echo "❌ 服务启动超时"
        docker logs openhands-app --tail 30
        exit 1
    fi
    
    sleep 10
done

echo ""
echo "🔍 验证MCP服务器..."

# 检查并启动MCP服务器
cd /www/wwwroot/ai.guiyunai.fun/mcp-proxies

if ! curl -s -f http://localhost:8080/sse >/dev/null 2>&1; then
    echo "启动文件系统MCP服务器..."
    nohup npx -y supergateway \
        --stdio "npx -y @modelcontextprotocol/server-filesystem /www/wwwroot/ai.guiyunai.fun/workspace" \
        --port 8080 \
        --ssePath /sse \
        --messagePath /message \
        --logLevel info > logs/filesystem.log 2>&1 &
fi

if ! curl -s -f http://localhost:8083/sse >/dev/null 2>&1; then
    echo "启动内存MCP服务器..."
    nohup npx -y supergateway \
        --stdio "npx -y @modelcontextprotocol/server-memory" \
        --port 8083 \
        --ssePath /sse \
        --messagePath /message \
        --logLevel info > logs/memory.log 2>&1 &
fi

sleep 5

echo ""
echo "🎉 部署验证完成！"
echo "================================"

echo "📊 最终状态报告:"
echo ""

# 容器状态
echo "🐳 容器状态:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep openhands-app

echo ""
echo "🔌 服务状态:"
LOCAL_HTTP=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
echo "- OpenHands Web: HTTP $LOCAL_HTTP"

MCP_FS=$(curl -s -f http://localhost:8080/sse >/dev/null 2>&1 && echo "✅ 正常" || echo "❌ 异常")
echo "- MCP文件系统: $MCP_FS"

MCP_MEM=$(curl -s -f http://localhost:8083/sse >/dev/null 2>&1 && echo "✅ 正常" || echo "❌ 异常")
echo "- MCP内存服务: $MCP_MEM"

echo ""
echo "🌐 访问地址:"
echo "- 本地访问: http://localhost:3000"
echo "- 外部访问: https://ai.guiyunai.fun"

echo ""
if [ "$LOCAL_HTTP" = "200" ]; then
    echo "🎯 部署成功！请进行以下验证:"
    echo "1. 打开浏览器访问 http://localhost:3000"
    echo "2. 检查界面是否正常加载"
    echo "3. 进入设置查看MCP配置选项"
    echo "4. 测试AI对话功能"
    echo "5. 验证文件操作和工具调用"
else
    echo "⚠️ 部署可能有问题，请检查:"
    echo "1. 查看容器日志: docker logs openhands-app"
    echo "2. 检查端口占用: netstat -tlnp | grep 3000"
    echo "3. 验证Docker权限: docker info"
fi

#!/bin/bash

# OpenHands MCP管理脚本
# 用于启动、停止、重启和监控MCP服务器

MCP_DIR="/www/wwwroot/ai.guiyunai.fun/mcp-proxies"
OPENHANDS_DIR="/www/wwwroot/ai.guiyunai.fun/OpenHands"

show_help() {
    echo "🔧 OpenHands MCP管理器"
    echo "================================"
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动MCP代理服务器"
    echo "  stop      停止MCP代理服务器"
    echo "  restart   重启MCP代理服务器"
    echo "  status    检查MCP服务器状态"
    echo "  logs      查看MCP服务器日志"
    echo "  test      测试MCP配置"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动所有MCP服务器"
    echo "  $0 status   # 检查服务器状态"
    echo "  $0 logs     # 查看日志"
}

start_mcp() {
    echo "🚀 启动MCP代理服务器..."
    cd "$MCP_DIR"
    if [ -f "start_basic_mcp.sh" ]; then
        ./start_basic_mcp.sh
    else
        echo "❌ 启动脚本不存在: $MCP_DIR/start_basic_mcp.sh"
        return 1
    fi
}

stop_mcp() {
    echo "🛑 停止MCP代理服务器..."
    pkill -f supergateway
    echo "✅ MCP服务器已停止"
}

restart_mcp() {
    echo "🔄 重启MCP代理服务器..."
    stop_mcp
    sleep 3
    start_mcp
}

check_status() {
    echo "📊 检查MCP服务器状态..."
    echo ""
    
    # 检查进程
    echo "🔍 运行中的SuperGateway进程:"
    ps aux | grep supergateway | grep -v grep || echo "  无运行中的进程"
    
    echo ""
    echo "🌐 端口检查:"
    for port in 8080 8083; do
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:$port/sse | grep -q "200\|404"; then
            echo "  ✅ 端口 $port: 服务正常"
        else
            echo "  ❌ 端口 $port: 服务异常"
        fi
    done
    
    echo ""
    echo "🐳 OpenHands容器状态:"
    docker ps | grep openhands-app || echo "  OpenHands容器未运行"
}

view_logs() {
    echo "📋 查看MCP服务器日志..."
    echo ""
    
    if [ -d "$MCP_DIR/logs" ]; then
        echo "📁 可用日志文件:"
        ls -la "$MCP_DIR/logs/"
        echo ""
        
        echo "📄 文件系统服务器日志 (最近10行):"
        tail -10 "$MCP_DIR/logs/filesystem.log" 2>/dev/null || echo "  日志文件不存在"
        
        echo ""
        echo "📄 内存服务器日志 (最近10行):"
        tail -10 "$MCP_DIR/logs/memory.log" 2>/dev/null || echo "  日志文件不存在"
    else
        echo "❌ 日志目录不存在: $MCP_DIR/logs"
    fi
}

test_mcp() {
    echo "🧪 测试MCP配置..."
    if [ -f "/www/wwwroot/ai.guiyunai.fun/test_mcp_configuration.sh" ]; then
        cd /www/wwwroot/ai.guiyunai.fun
        ./test_mcp_configuration.sh
    else
        echo "❌ 测试脚本不存在"
    fi
}

# 主逻辑
case "$1" in
    start)
        start_mcp
        ;;
    stop)
        stop_mcp
        ;;
    restart)
        restart_mcp
        ;;
    status)
        check_status
        ;;
    logs)
        view_logs
        ;;
    test)
        test_mcp
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac

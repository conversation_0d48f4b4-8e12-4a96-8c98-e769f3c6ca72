# 🔌 项目端口使用报告

## 📊 当前占用的端口

### 🎯 **核心服务端口（不可占用）**

#### **1. OpenHands主服务**
- **端口**: `3000`
- **服务**: OpenHands Web界面
- **状态**: ✅ 运行中
- **用途**: AI代理Web界面
- **重要性**: 🔴 **核心服务，不可占用**

#### **2. MCP代理服务器**
- **端口**: `8080` - 文件系统MCP服务器
- **端口**: `8083` - 内存MCP服务器  
- **服务**: SuperGateway MCP代理
- **状态**: ✅ 运行中
- **用途**: Model Context Protocol服务
- **重要性**: 🔴 **核心功能，不可占用**

#### **3. 系统基础服务**
- **端口**: `22` - SSH服务
- **端口**: `80` - Nginx HTTP
- **端口**: `443` - Nginx HTTPS
- **状态**: ✅ 运行中
- **重要性**: 🔴 **系统服务，不可占用**

### 🐳 **Docker动态端口（避免范围）**

#### **OpenHands运行时容器端口**
这些端口由Docker自动分配，会动态变化：

- **当前使用的端口范围**: `35000-60000`
- **具体端口**:
  - `35191`, `37486`, `36764` (运行时容器1)
  - `41820`, `48884`, `40938` (运行时容器2) 
  - `53610`, `52846`, `55842` (运行时容器3)
  - `58556`, `58625`, `54717` (其他动态端口)

- **建议**: 🟡 **避免使用35000-60000端口范围**

### 💻 **开发工具端口**
- **端口**: `37375`, `43473` - VS Code服务器
- **状态**: ✅ 运行中
- **重要性**: 🟡 **开发工具，建议保留**

## ✅ **可用端口范围**

### 🟢 **推荐使用的端口范围**

#### **Web服务端口**
- `4000-4999` - Web应用服务
- `5000-5999` - API服务
- `6000-6999` - 微服务
- `7000-7999` - 数据库服务

#### **特定服务推荐端口**
- `4000` - React/Vue开发服务器
- `5000` - Flask/Express API
- `6379` - Redis (标准端口)
- `3306` - MySQL (标准端口)
- `5432` - PostgreSQL (标准端口)
- `27017` - MongoDB (标准端口)
- `9000` - 管理面板/监控服务

### 🟡 **谨慎使用的端口**
- `8000-8999` - 可用，但避开8080, 8083
- `9000-9999` - 可用，常用于管理服务
- `10000+` - 高端口，通常安全

## ❌ **禁止使用的端口**

### 🔴 **绝对不可占用**
- `22` - SSH (系统关键)
- `80` - HTTP (Nginx)
- `443` - HTTPS (Nginx)
- `3000` - OpenHands主服务
- `8080` - MCP文件系统服务器
- `8083` - MCP内存服务器

### 🟡 **避免使用**
- `35000-60000` - Docker动态端口范围
- `37375`, `43473` - VS Code服务器
- `1-1023` - 系统保留端口

## 🚀 **新项目部署建议**

### **端口分配策略**
```bash
# Web前端服务
4000-4099: React/Vue/Angular应用

# API后端服务  
5000-5099: REST API服务
5100-5199: GraphQL服务

# 数据库服务
6000-6099: 关系型数据库
6100-6199: NoSQL数据库
6200-6299: 缓存服务

# 微服务
7000-7999: 业务微服务

# 管理和监控
9000-9099: 管理面板
9100-9199: 监控服务
9200-9299: 日志服务
```

### **检查端口是否可用**
```bash
# 检查端口是否被占用
netstat -tlnp | grep :端口号

# 或使用ss命令
ss -tlnp | grep :端口号

# 测试端口连通性
curl -s http://localhost:端口号
```

## 📋 **端口管理工具**

### **查看所有监听端口**
```bash
ss -tlnp | grep LISTEN | sort -n
```

### **查看Docker容器端口**
```bash
docker ps --format "table {{.Names}}\t{{.Ports}}"
```

### **查看特定端口使用情况**
```bash
lsof -i :端口号
```

## 🎯 **总结**

### ✅ **安全使用的端口**
- `4000-4999` (Web服务)
- `5000-5999` (API服务) 
- `6000-6999` (数据库)
- `7000-7999` (微服务)
- `9000-9999` (管理服务)

### ❌ **必须避免的端口**
- `3000` (OpenHands)
- `8080`, `8083` (MCP服务)
- `80`, `443` (Nginx)
- `22` (SSH)
- `35000-60000` (Docker动态端口)

建议在部署新项目前，先使用端口检查命令确认端口可用性！

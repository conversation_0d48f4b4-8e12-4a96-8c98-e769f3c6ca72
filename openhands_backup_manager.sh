#!/bin/bash

# OpenHands备份管理界面
# 版本: 2.0
# 作者: 系统管理员
# 日期: 2025-08-04

set -e

PROJECT_DIR="/www/wwwroot/ai.guiyunai.fun"
BACKUP_BASE_DIR="/backup/openhands"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 显示主菜单
show_main_menu() {
    clear
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                OpenHands备份管理系统                        ║${NC}"
    echo -e "${CYAN}║                    版本 2.0                                  ║${NC}"
    echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  ${GREEN}1.${NC} 创建新备份                                           ${CYAN}║${NC}"
    echo -e "${CYAN}║  ${GREEN}2.${NC} 查看备份列表                                         ${CYAN}║${NC}"
    echo -e "${CYAN}║  ${GREEN}3.${NC} 恢复备份                                             ${CYAN}║${NC}"
    echo -e "${CYAN}║  ${GREEN}4.${NC} 删除备份                                             ${CYAN}║${NC}"
    echo -e "${CYAN}║  ${GREEN}5.${NC} 系统状态                                             ${CYAN}║${NC}"
    echo -e "${CYAN}║  ${GREEN}6.${NC} 配置保护                                             ${CYAN}║${NC}"
    echo -e "${CYAN}║  ${GREEN}7.${NC} 运行测试                                             ${CYAN}║${NC}"
    echo -e "${CYAN}║  ${GREEN}8.${NC} 查看日志                                             ${CYAN}║${NC}"
    echo -e "${CYAN}║  ${GREEN}9.${NC} 设置定时任务                                         ${CYAN}║${NC}"
    echo -e "${CYAN}║  ${GREEN}0.${NC} 退出                                                 ${CYAN}║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 显示系统状态
show_system_status() {
    echo -e "${BLUE}📊 系统状态概览${NC}"
    echo "================================"
    
    # OpenHands服务状态
    if docker ps | grep -q openhands-app; then
        echo -e "🟢 OpenHands服务: ${GREEN}运行中${NC}"
    else
        echo -e "🔴 OpenHands服务: ${RED}未运行${NC}"
    fi
    
    # 运行时容器状态
    local runtime_count=$(docker ps | grep runtime | wc -l)
    if [ $runtime_count -gt 0 ]; then
        echo -e "🟢 运行时容器: ${GREEN}$runtime_count 个运行中${NC}"
    else
        echo -e "🔴 运行时容器: ${RED}未运行${NC}"
    fi
    
    # HTTP服务状态
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
    if [ "$http_code" = "200" ]; then
        echo -e "🟢 HTTP服务: ${GREEN}正常 (HTTP $http_code)${NC}"
    else
        echo -e "🔴 HTTP服务: ${RED}异常 (HTTP $http_code)${NC}"
    fi
    
    # 域名访问状态
    local domain_code=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null)
    if [ "$domain_code" = "200" ]; then
        echo -e "🟢 域名访问: ${GREEN}正常 (HTTP $domain_code)${NC}"
    else
        echo -e "🔴 域名访问: ${RED}异常 (HTTP $domain_code)${NC}"
    fi
    
    echo ""
    
    # 备份状态
    echo -e "${BLUE}💾 备份状态${NC}"
    echo "================================"
    
    if [ -d "$BACKUP_BASE_DIR" ]; then
        local backup_count=$(ls -1 "$BACKUP_BASE_DIR" | grep -E '^[0-9]{8}_[0-9]{6}$' | wc -l)
        echo "📦 可用备份数量: $backup_count"
        
        if [ -L "$BACKUP_BASE_DIR/latest" ]; then
            local latest_backup=$(readlink "$BACKUP_BASE_DIR/latest")
            local backup_name=$(basename "$latest_backup")
            local backup_size=$(du -sh "$latest_backup" 2>/dev/null | cut -f1)
            echo "📅 最新备份: $backup_name ($backup_size)"
        else
            echo "📅 最新备份: 无"
        fi
    else
        echo "❌ 备份目录不存在"
    fi
    
    echo ""
    
    # 系统资源
    echo -e "${BLUE}🖥️ 系统资源${NC}"
    echo "================================"
    
    local mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    
    echo "💾 内存使用: ${mem_usage}%"
    echo "💿 磁盘使用: ${disk_usage}%"
    
    # Docker资源
    echo ""
    echo -e "${BLUE}🐳 Docker状态${NC}"
    echo "================================"
    docker system df --format "table {{.Type}}\t{{.TotalCount}}\t{{.Size}}\t{{.Reclaimable}}"
}

# 创建新备份
create_backup() {
    echo -e "${BLUE}📦 创建新备份${NC}"
    echo "================================"
    
    echo "正在创建备份，请稍候..."
    
    if bash "${PROJECT_DIR}/openhands_backup_system.sh"; then
        echo -e "${GREEN}✅ 备份创建成功！${NC}"
    else
        echo -e "${RED}❌ 备份创建失败！${NC}"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 查看备份列表
list_backups() {
    echo -e "${BLUE}📋 备份列表${NC}"
    echo "================================"
    
    if [ ! -d "$BACKUP_BASE_DIR" ]; then
        echo "❌ 备份目录不存在"
        read -p "按回车键继续..."
        return
    fi
    
    cd "$BACKUP_BASE_DIR"
    local backups=($(ls -1t | grep -E '^[0-9]{8}_[0-9]{6}$'))
    
    if [ ${#backups[@]} -eq 0 ]; then
        echo "📭 没有找到备份文件"
        read -p "按回车键继续..."
        return
    fi
    
    echo "序号  备份时间              大小     文件数"
    echo "----  ------------------    ------   ------"
    
    for i in "${!backups[@]}"; do
        local backup_dir="${backups[$i]}"
        local backup_path="$BACKUP_BASE_DIR/$backup_dir"
        local size=$(du -sh "$backup_path" 2>/dev/null | cut -f1)
        local file_count=$(find "$backup_path" -type f | wc -l)
        local date_formatted=$(echo "$backup_dir" | sed 's/_/ /' | sed 's/\([0-9]\{4\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)/\1-\2-\3/')
        
        printf "%-4s  %-18s  %-8s %-6s\n" "$((i+1))" "$date_formatted" "$size" "$file_count"
    done
    
    echo ""
    read -p "按回车键继续..."
}

# 恢复备份
restore_backup() {
    echo -e "${BLUE}🔄 恢复备份${NC}"
    echo "================================"
    
    bash "${PROJECT_DIR}/openhands_rollback_system.sh" -i
    
    echo ""
    read -p "按回车键继续..."
}

# 配置保护管理
manage_protection() {
    while true; do
        clear
        echo -e "${BLUE}🛡️ 配置保护管理${NC}"
        echo "================================"
        
        # 显示保护状态
        if pgrep -f "openhands_config_protection.*monitor" >/dev/null; then
            echo -e "🟢 监控状态: ${GREEN}运行中${NC}"
        else
            echo -e "🔴 监控状态: ${RED}未运行${NC}"
        fi
        
        echo ""
        echo "1. 启动监控"
        echo "2. 停止监控"
        echo "3. 生成校验和"
        echo "4. 验证完整性"
        echo "5. 启用文件保护"
        echo "6. 移除文件保护"
        echo "7. 健康检查"
        echo "0. 返回主菜单"
        echo ""
        
        read -p "请选择操作: " choice
        
        case $choice in
            1)
                bash "${PROJECT_DIR}/openhands_config_protection.sh" --start-monitoring
                ;;
            2)
                bash "${PROJECT_DIR}/openhands_config_protection.sh" --stop-monitoring
                ;;
            3)
                bash "${PROJECT_DIR}/openhands_config_protection.sh" --generate-checksums
                ;;
            4)
                bash "${PROJECT_DIR}/openhands_config_protection.sh" --verify
                ;;
            5)
                bash "${PROJECT_DIR}/openhands_config_protection.sh" --protect
                ;;
            6)
                bash "${PROJECT_DIR}/openhands_config_protection.sh" --unprotect
                ;;
            7)
                bash "${PROJECT_DIR}/openhands_config_protection.sh" --health-check
                ;;
            0)
                break
                ;;
            *)
                echo "无效选择"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

# 运行测试
run_tests() {
    echo -e "${BLUE}🧪 运行系统测试${NC}"
    echo "================================"
    
    echo "1. 运行所有测试"
    echo "2. 备份性能测试"
    echo "3. 配置保护测试"
    echo "4. 健康检查测试"
    echo "0. 返回主菜单"
    echo ""
    
    read -p "请选择测试类型: " choice
    
    case $choice in
        1)
            bash "${PROJECT_DIR}/openhands_backup_test.sh" --all
            ;;
        2)
            bash "${PROJECT_DIR}/openhands_backup_test.sh" --performance
            ;;
        3)
            bash "${PROJECT_DIR}/openhands_backup_test.sh" --protection
            ;;
        4)
            bash "${PROJECT_DIR}/openhands_backup_test.sh" --health
            ;;
        0)
            return
            ;;
        *)
            echo "无效选择"
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
}

# 设置定时任务
setup_cron() {
    echo -e "${BLUE}⏰ 设置定时备份${NC}"
    echo "================================"
    
    echo "当前定时任务:"
    crontab -l | grep -E "(backup|openhands)" || echo "无相关定时任务"
    
    echo ""
    echo "建议的定时任务配置:"
    echo "1. 每日凌晨2点自动备份"
    echo "2. 每小时检查系统健康"
    echo "3. 每天清理旧备份"
    echo ""
    
    read -p "是否设置自动备份定时任务? (y/N): " confirm
    
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        # 添加定时任务
        (crontab -l 2>/dev/null; echo "0 2 * * * cd ${PROJECT_DIR} && ./openhands_backup_system.sh >/dev/null 2>&1") | crontab -
        (crontab -l 2>/dev/null; echo "0 * * * * cd ${PROJECT_DIR} && ./openhands_config_protection.sh --health-check >/dev/null 2>&1") | crontab -
        
        echo -e "${GREEN}✅ 定时任务设置完成${NC}"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 查看日志
view_logs() {
    echo -e "${BLUE}📄 查看系统日志${NC}"
    echo "================================"
    
    echo "1. 备份日志"
    echo "2. 回滚日志"
    echo "3. 保护系统日志"
    echo "4. OpenHands容器日志"
    echo "5. Nginx日志"
    echo "0. 返回主菜单"
    echo ""
    
    read -p "请选择日志类型: " choice
    
    case $choice in
        1)
            if [ -f "/var/log/openhands_backup.log" ]; then
                tail -50 /var/log/openhands_backup.log
            else
                echo "备份日志文件不存在"
            fi
            ;;
        2)
            if [ -f "/var/log/openhands_rollback.log" ]; then
                tail -50 /var/log/openhands_rollback.log
            else
                echo "回滚日志文件不存在"
            fi
            ;;
        3)
            if [ -f "/var/log/openhands_protection.log" ]; then
                tail -50 /var/log/openhands_protection.log
            else
                echo "保护系统日志文件不存在"
            fi
            ;;
        4)
            docker logs openhands-app --tail 50
            ;;
        5)
            tail -50 /var/log/nginx/ai.guiyunai.fun.access.log
            ;;
        0)
            return
            ;;
        *)
            echo "无效选择"
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
}

# 主循环
main() {
    while true; do
        show_main_menu
        read -p "请选择操作 (0-9): " choice
        
        case $choice in
            1)
                create_backup
                ;;
            2)
                list_backups
                ;;
            3)
                restore_backup
                ;;
            4)
                echo "删除备份功能暂未实现"
                read -p "按回车键继续..."
                ;;
            5)
                clear
                show_system_status
                echo ""
                read -p "按回车键继续..."
                ;;
            6)
                manage_protection
                ;;
            7)
                run_tests
                ;;
            8)
                view_logs
                ;;
            9)
                setup_cron
                ;;
            0)
                echo -e "${GREEN}感谢使用OpenHands备份管理系统！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重新输入${NC}"
                sleep 1
                ;;
        esac
    done
}

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请使用root权限运行此脚本${NC}"
    exit 1
fi

# 启动主程序
main

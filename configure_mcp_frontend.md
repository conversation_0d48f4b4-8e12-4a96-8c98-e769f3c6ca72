# OpenHands前端MCP配置指南

## 🎯 问题说明

经过深入分析，发现OpenHands的MCP配置需要通过前端界面手动配置，而不是通过配置文件。当前系统已经成功部署了8个MCP服务器，但需要在前端进行配置才能使用。

## 📋 当前MCP服务器状态

### ✅ 已成功部署的MCP服务器

| 端口 | 服务器名称 | SSE端点 | 功能描述 |
|------|------------|---------|----------|
| 8080 | 文件系统 | http://host.docker.internal:8080/sse | 安全的文件操作 |
| 8081 | 内存 | http://host.docker.internal:8081/sse | 知识图谱持久化内存 |
| 8082 | 网页获取 | http://host.docker.internal:8082/sse | 网页内容获取和转换 |
| 8084 | Git | http://host.docker.internal:8084/sse | Git仓库操作和管理 |
| 8090 | 营销推广 | http://host.docker.internal:8090/sse | 营销推广和内容创作 |
| 8100 | 剧本杀 | http://host.docker.internal:8100/sse | 剧本创作和角色设计 |
| 8101 | 设计创意 | http://host.docker.internal:8101/sse | 设计创意和视觉内容创作 |
| 8110 | 编程开发 | http://host.docker.internal:8110/sse | 编程开发和技术学习 |

### 🔍 验证服务器状态

所有8个MCP服务器都在正常运行，可以通过以下命令验证：

```bash
cd /www/wwwroot/ai.guiyunai.fun
./check_mcp_status.sh
```

## 🔧 前端配置步骤

### 步骤1: 访问OpenHands前端

1. 打开浏览器访问: https://ai.guiyunai.fun
2. 等待页面完全加载

### 步骤2: 查找MCP配置选项

在前端界面中查找以下可能的配置位置：

1. **设置页面** (Settings)
   - 查找"MCP"、"Model Context Protocol"或"工具"相关选项
   - 可能在"高级设置"或"开发者选项"中

2. **工具配置** (Tools Configuration)
   - 查找"外部工具"或"插件"配置
   - 可能在主界面的工具栏或菜单中

3. **连接设置** (Connections)
   - 查找"外部服务"或"API连接"配置
   - 可能在用户配置或系统设置中

### 步骤3: 添加MCP服务器

如果找到MCP配置选项，按以下格式添加服务器：

#### 基础开发工具类
```
名称: 文件系统服务器
URL: http://host.docker.internal:8080/sse
类型: SSE
描述: 安全的文件操作

名称: 内存服务器  
URL: http://host.docker.internal:8081/sse
类型: SSE
描述: 知识图谱持久化内存

名称: 网页获取服务器
URL: http://host.docker.internal:8082/sse
类型: SSE
描述: 网页内容获取和转换

名称: Git服务器
URL: http://host.docker.internal:8084/sse
类型: SSE
描述: Git仓库操作和管理
```

#### 特定领域工具类
```
名称: 营销推广服务器
URL: http://host.docker.internal:8090/sse
类型: SSE
描述: 营销推广和内容创作

名称: 剧本杀服务器
URL: http://host.docker.internal:8100/sse
类型: SSE
描述: 剧本创作和角色设计

名称: 设计创意服务器
URL: http://host.docker.internal:8101/sse
类型: SSE
描述: 设计创意和视觉内容创作

名称: 编程开发服务器
URL: http://host.docker.internal:8110/sse
类型: SSE
描述: 编程开发和技术学习
```

## 🔍 替代配置方法

### 方法1: 检查用户配置文件

OpenHands可能将MCP配置存储在用户配置文件中：

```bash
# 检查用户配置目录
ls -la /www/wwwroot/ai.guiyunai.fun/.openhands/

# 查找配置文件
find /www/wwwroot/ai.guiyunai.fun/.openhands/ -name "*.json" -o -name "*.toml" -o -name "*.yaml"
```

### 方法2: 通过API配置

如果前端没有配置界面，可能需要通过API直接配置：

```bash
# 检查MCP API端点
curl -X GET http://localhost:3000/api/mcp/servers

# 添加MCP服务器（示例）
curl -X POST http://localhost:3000/api/mcp/servers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "文件系统服务器",
    "url": "http://host.docker.internal:8080/sse",
    "type": "sse"
  }'
```

### 方法3: 环境变量配置（已尝试）

我们已经尝试了环境变量配置，但OpenHands似乎没有读取这些配置。

## 🧪 测试MCP功能

配置完成后，可以通过以下方式测试MCP功能：

### 1. 创建新对话
在OpenHands前端创建新对话，测试是否能看到新的工具。

### 2. 检查工具列表
在对话中，查看可用工具列表，应该能看到我们配置的MCP工具。

### 3. 测试具体功能
尝试使用各种MCP工具：
- 文件操作
- 内存存储
- 网页获取
- Git操作
- 营销创作
- 剧本创作
- 设计创意
- 编程开发

## 🔧 管理命令

### 启动所有MCP服务器
```bash
cd /www/wwwroot/ai.guiyunai.fun
./mcp-proxies/official/start_official_mcp_proxies.sh start
./mcp-proxies/marketing/start_marketing_mcp_proxies.sh start
./mcp-proxies/creative/start_creative_mcp_proxies.sh start
./mcp-proxies/career/start_career_mcp_proxies.sh start
```

### 检查服务器状态
```bash
cd /www/wwwroot/ai.guiyunai.fun
./check_mcp_status.sh
```

### 重启OpenHands服务
```bash
docker restart openhands-app
```

## 📞 技术支持

如果在前端找不到MCP配置选项，可能需要：

1. **升级OpenHands版本**: 确保使用支持MCP配置的版本
2. **查看官方文档**: 检查最新的MCP配置方法
3. **社区支持**: 在OpenHands社区寻求帮助

## 🎯 下一步行动

1. **立即行动**: 在前端界面中查找MCP配置选项
2. **验证功能**: 配置完成后测试各个MCP工具
3. **文档更新**: 记录实际的配置步骤供后续参考

---

**注意**: 所有MCP服务器已经成功部署并运行，只需要在前端进行配置即可使用全部功能。

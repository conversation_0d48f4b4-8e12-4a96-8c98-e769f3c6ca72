#!/bin/bash

echo "🌐 OpenHands域名绑定配置脚本"
echo "================================"

echo "🔍 步骤1: 检查当前Nginx配置..."

# 检查当前配置文件
echo "当前ai.guiyunai.fun配置:"
if [ -f "/etc/nginx/sites-available/ai.guiyunai.fun" ]; then
    echo "配置文件存在，查看内容..."
    head -20 /etc/nginx/sites-available/ai.guiyunai.fun
else
    echo "配置文件不存在"
fi

echo ""
echo "🔍 步骤2: 检查当前服务状态..."

# 检查OpenHands服务
echo "OpenHands服务状态:"
curl -s -o /dev/null -w "本地访问(3000): HTTP %{http_code}\n" http://localhost:3000

# 检查当前域名访问
echo "当前域名访问:"
curl -s -o /dev/null -w "域名访问: HTTP %{http_code}\n" https://ai.guiyunai.fun

echo ""
echo "🔧 步骤3: 创建OpenHands反向代理配置..."

# 备份当前配置
if [ -f "/etc/nginx/sites-available/ai.guiyunai.fun" ]; then
    cp /etc/nginx/sites-available/ai.guiyunai.fun /etc/nginx/sites-available/ai.guiyunai.fun.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 当前配置已备份"
fi

# 创建新的Nginx配置
cat > /etc/nginx/sites-available/ai.guiyunai.fun << 'EOF'
server {
    listen 80;
    listen [::]:80;
    server_name ai.guiyunai.fun;
    
    # HTTP重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name ai.guiyunai.fun;

    # SSL配置
    ssl_certificate /etc/letsencrypt/live/ai.guiyunai.fun/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ai.guiyunai.fun/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 客户端最大上传大小
    client_max_body_size 100M;

    # OpenHands主应用代理
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        
        # WebSocket支持
        proxy_set_header Sec-WebSocket-Extensions $http_sec_websocket_extensions;
        proxy_set_header Sec-WebSocket-Key $http_sec_websocket_key;
        proxy_set_header Sec-WebSocket-Version $http_sec_websocket_version;
    }

    # MCP文件系统服务器代理
    location /mcp/filesystem/ {
        proxy_pass http://localhost:8080/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # SSE支持
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 24h;
        proxy_send_timeout 24h;
    }

    # MCP内存服务器代理
    location /mcp/memory/ {
        proxy_pass http://localhost:8083/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # SSE支持
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 24h;
        proxy_send_timeout 24h;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://localhost:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 日志配置
    access_log /var/log/nginx/ai.guiyunai.fun.access.log;
    error_log /var/log/nginx/ai.guiyunai.fun.error.log;
}
EOF

echo "✅ 新的Nginx配置已创建"

echo ""
echo "🔧 步骤4: 验证配置并重载Nginx..."

# 测试Nginx配置
if nginx -t; then
    echo "✅ Nginx配置语法正确"
    
    # 重载Nginx
    systemctl reload nginx
    echo "✅ Nginx已重载"
else
    echo "❌ Nginx配置有错误，请检查"
    exit 1
fi

echo ""
echo "🔍 步骤5: 验证域名绑定..."

# 等待配置生效
sleep 5

# 测试域名访问
echo "测试域名访问:"
DOMAIN_HTTP=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null)
echo "HTTPS访问: HTTP $DOMAIN_HTTP"

# 测试MCP端点
echo ""
echo "测试MCP端点:"
MCP_FS=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/mcp/filesystem/sse 2>/dev/null)
echo "MCP文件系统: HTTP $MCP_FS"

MCP_MEM=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/mcp/memory/sse 2>/dev/null)
echo "MCP内存服务: HTTP $MCP_MEM"

echo ""
echo "🎉 域名绑定配置完成！"
echo "================================"

echo "📊 最终状态报告:"
echo "- OpenHands主应用: https://ai.guiyunai.fun"
echo "- MCP文件系统: https://ai.guiyunai.fun/mcp/filesystem/"
echo "- MCP内存服务: https://ai.guiyunai.fun/mcp/memory/"

echo ""
echo "🔧 配置说明:"
echo "1. 主域名直接代理到OpenHands (端口3000)"
echo "2. MCP服务通过子路径代理 (/mcp/filesystem/, /mcp/memory/)"
echo "3. 支持WebSocket和SSE连接"
echo "4. 启用了SSL/TLS加密"
echo "5. 添加了安全头和缓存优化"

echo ""
echo "📋 下一步操作:"
echo "1. 访问 https://ai.guiyunai.fun 验证OpenHands界面"
echo "2. 在OpenHands设置中配置MCP服务器:"
echo "   - 文件系统: https://ai.guiyunai.fun/mcp/filesystem/sse"
echo "   - 内存服务: https://ai.guiyunai.fun/mcp/memory/sse"
echo "3. 测试AI对话和工具调用功能"

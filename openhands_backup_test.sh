#!/bin/bash

# OpenHands备份系统测试和验证脚本
# 版本: 2.0
# 作者: 系统管理员
# 日期: 2025-08-04

set -e

# 配置变量
PROJECT_DIR="/www/wwwroot/ai.guiyunai.fun"
BACKUP_BASE_DIR="/backup/openhands"
TEST_DIR="/tmp/openhands_backup_test"
LOG_FILE="/var/log/openhands_backup_test.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

# 测试备份完整性
test_backup_integrity() {
    local backup_dir="$1"
    local test_name="备份完整性测试"
    
    log "开始 $test_name: $backup_dir"
    
    local errors=0
    
    # 检查必要文件
    local required_files=(
        "restore.sh"
        "MANIFEST.txt"
        "system_info.txt"
        "data/openhands_user_data.tar.gz"
        "config"
        "nginx"
        "docker"
        "mcp"
    )
    
    for item in "${required_files[@]}"; do
        if [ ! -e "$backup_dir/$item" ]; then
            error "缺少必要文件/目录: $item"
            ((errors++))
        else
            info "✅ 存在: $item"
        fi
    done
    
    # 检查压缩文件完整性
    local tar_files=(
        "data/openhands_user_data.tar.gz"
        "data/workspace.tar.gz"
        "data/project_files.tar.gz"
        "logs/nginx_logs.tar.gz"
    )
    
    for tar_file in "${tar_files[@]}"; do
        if [ -f "$backup_dir/$tar_file" ]; then
            if tar -tzf "$backup_dir/$tar_file" >/dev/null 2>&1; then
                info "✅ 压缩文件完整: $tar_file"
            else
                error "压缩文件损坏: $tar_file"
                ((errors++))
            fi
        fi
    done
    
    # 检查恢复脚本语法
    if [ -f "$backup_dir/restore.sh" ]; then
        if bash -n "$backup_dir/restore.sh"; then
            info "✅ 恢复脚本语法正确"
        else
            error "恢复脚本语法错误"
            ((errors++))
        fi
    fi
    
    if [ $errors -eq 0 ]; then
        log "✅ $test_name 通过"
        return 0
    else
        error "❌ $test_name 失败 ($errors 个错误)"
        return 1
    fi
}

# 测试恢复功能（模拟环境）
test_restore_simulation() {
    local backup_dir="$1"
    local test_name="恢复功能模拟测试"
    
    log "开始 $test_name: $backup_dir"
    
    # 创建测试环境
    local test_env="$TEST_DIR/restore_test_$(date +%s)"
    mkdir -p "$test_env"/{project,nginx,docker}
    
    # 模拟项目目录结构
    mkdir -p "$test_env/project"/{.openhands,workspace,mcp_servers}
    
    # 复制恢复脚本到测试环境
    cp "$backup_dir/restore.sh" "$test_env/"
    
    # 修改恢复脚本中的路径（用于测试）
    sed -i "s|/www/wwwroot/ai.guiyunai.fun|$test_env/project|g" "$test_env/restore.sh"
    sed -i "s|/etc/nginx|$test_env/nginx|g" "$test_env/restore.sh"
    sed -i "s|systemctl|echo systemctl|g" "$test_env/restore.sh"
    sed -i "s|docker|echo docker|g" "$test_env/restore.sh"
    
    # 执行模拟恢复
    cd "$test_env"
    if bash restore.sh >/dev/null 2>&1; then
        log "✅ $test_name 通过"
        rm -rf "$test_env"
        return 0
    else
        error "❌ $test_name 失败"
        rm -rf "$test_env"
        return 1
    fi
}

# 测试备份性能
test_backup_performance() {
    local test_name="备份性能测试"
    
    log "开始 $test_name"
    
    local start_time=$(date +%s)
    
    # 执行备份
    if bash "${PROJECT_DIR}/openhands_backup_system.sh" >/dev/null 2>&1; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log "✅ $test_name 通过"
        info "备份耗时: ${duration}秒"
        
        # 检查备份大小
        local latest_backup="${BACKUP_BASE_DIR}/latest"
        if [ -L "$latest_backup" ]; then
            local backup_size=$(du -sh "$(readlink "$latest_backup")" | cut -f1)
            info "备份大小: $backup_size"
        fi
        
        return 0
    else
        error "❌ $test_name 失败"
        return 1
    fi
}

# 测试回滚功能
test_rollback_functionality() {
    local test_name="回滚功能测试"
    
    log "开始 $test_name"
    
    # 检查是否有可用备份
    if [ ! -L "${BACKUP_BASE_DIR}/latest" ]; then
        warning "没有可用备份，跳过回滚测试"
        return 0
    fi
    
    # 创建测试标记文件
    local test_marker="${PROJECT_DIR}/test_rollback_marker_$(date +%s)"
    echo "测试标记" > "$test_marker"
    
    # 记录当前状态
    local current_config_hash=""
    if [ -f "${PROJECT_DIR}/OpenHands/config.toml" ]; then
        current_config_hash=$(md5sum "${PROJECT_DIR}/OpenHands/config.toml" | awk '{print $1}')
    fi
    
    # 模拟回滚（不实际执行，只验证脚本）
    if bash -n "${PROJECT_DIR}/openhands_rollback_system.sh"; then
        log "✅ $test_name 通过（脚本语法验证）"
        rm -f "$test_marker"
        return 0
    else
        error "❌ $test_name 失败"
        rm -f "$test_marker"
        return 1
    fi
}

# 测试配置保护功能
test_config_protection() {
    local test_name="配置保护功能测试"
    
    log "开始 $test_name"
    
    # 初始化保护系统
    if bash "${PROJECT_DIR}/openhands_config_protection.sh" --init >/dev/null 2>&1; then
        info "✅ 保护系统初始化成功"
    else
        error "保护系统初始化失败"
        return 1
    fi
    
    # 生成校验和
    if bash "${PROJECT_DIR}/openhands_config_protection.sh" --generate-checksums >/dev/null 2>&1; then
        info "✅ 校验和生成成功"
    else
        error "校验和生成失败"
        return 1
    fi
    
    # 验证完整性
    if bash "${PROJECT_DIR}/openhands_config_protection.sh" --verify >/dev/null 2>&1; then
        info "✅ 完整性验证成功"
    else
        error "完整性验证失败"
        return 1
    fi
    
    log "✅ $test_name 通过"
    return 0
}

# 测试系统健康检查
test_health_check() {
    local test_name="系统健康检查测试"
    
    log "开始 $test_name"
    
    if bash "${PROJECT_DIR}/openhands_config_protection.sh" --health-check >/dev/null 2>&1; then
        log "✅ $test_name 通过"
        return 0
    else
        warning "⚠️ $test_name 检测到问题（这可能是正常的）"
        return 0  # 健康检查失败不算测试失败
    fi
}

# 压力测试
stress_test() {
    local test_name="压力测试"
    
    log "开始 $test_name"
    
    local errors=0
    
    # 连续执行多次备份
    for i in {1..3}; do
        info "执行第 $i 次备份..."
        if ! bash "${PROJECT_DIR}/openhands_backup_system.sh" >/dev/null 2>&1; then
            error "第 $i 次备份失败"
            ((errors++))
        fi
        sleep 5
    done
    
    # 测试并发访问
    info "测试并发访问..."
    for i in {1..5}; do
        bash "${PROJECT_DIR}/openhands_config_protection.sh" --verify >/dev/null 2>&1 &
    done
    wait
    
    if [ $errors -eq 0 ]; then
        log "✅ $test_name 通过"
        return 0
    else
        error "❌ $test_name 失败 ($errors 个错误)"
        return 1
    fi
}

# 清理测试环境
cleanup_test_env() {
    log "清理测试环境..."
    
    # 清理测试目录
    rm -rf "$TEST_DIR"
    
    # 清理多余的备份（保留最新的3个）
    cd "$BACKUP_BASE_DIR"
    ls -1t | grep -E '^[0-9]{8}_[0-9]{6}$' | tail -n +4 | xargs -r rm -rf
    
    log "测试环境清理完成"
}

# 生成测试报告
generate_test_report() {
    local total_tests="$1"
    local passed_tests="$2"
    local failed_tests="$3"
    
    local report_file="${PROJECT_DIR}/backup_test_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# OpenHands备份系统测试报告

## 测试概要
- 测试时间: $(date)
- 总测试数: $total_tests
- 通过测试: $passed_tests
- 失败测试: $failed_tests
- 成功率: $(( passed_tests * 100 / total_tests ))%

## 测试结果详情

### 通过的测试
$(grep "✅.*通过" "$LOG_FILE" | tail -20)

### 失败的测试
$(grep "❌.*失败" "$LOG_FILE" | tail -10)

## 系统状态
- 备份目录: $BACKUP_BASE_DIR
- 可用备份数: $(ls -1 "$BACKUP_BASE_DIR" | grep -E '^[0-9]{8}_[0-9]{6}$' | wc -l)
- 最新备份: $(readlink "$BACKUP_BASE_DIR/latest" 2>/dev/null | xargs basename || echo "无")

## 建议
$(if [ $failed_tests -eq 0 ]; then
    echo "- 所有测试通过，备份系统运行正常"
    echo "- 建议定期执行测试以确保系统稳定性"
else
    echo "- 发现 $failed_tests 个问题，建议检查日志文件: $LOG_FILE"
    echo "- 建议修复问题后重新运行测试"
fi)

---
报告生成时间: $(date)
EOF

    log "测试报告已生成: $report_file"
}

# 主测试函数
run_all_tests() {
    echo "🧪 OpenHands备份系统完整测试"
    echo "============================="
    
    # 创建测试目录
    mkdir -p "$TEST_DIR"
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # 测试列表
    local tests=(
        "test_backup_performance"
        "test_config_protection"
        "test_health_check"
        "stress_test"
    )
    
    # 如果有备份，测试备份相关功能
    if [ -L "${BACKUP_BASE_DIR}/latest" ]; then
        local latest_backup=$(readlink "${BACKUP_BASE_DIR}/latest")
        tests+=("test_backup_integrity $latest_backup")
        tests+=("test_restore_simulation $latest_backup")
        tests+=("test_rollback_functionality")
    fi
    
    # 执行测试
    for test in "${tests[@]}"; do
        ((total_tests++))
        if eval "$test"; then
            ((passed_tests++))
        else
            ((failed_tests++))
        fi
        echo ""
    done
    
    # 清理测试环境
    cleanup_test_env
    
    # 生成报告
    generate_test_report "$total_tests" "$passed_tests" "$failed_tests"
    
    # 显示结果
    echo "📊 测试完成"
    echo "============"
    echo "总测试数: $total_tests"
    echo "通过: $passed_tests"
    echo "失败: $failed_tests"
    echo "成功率: $(( passed_tests * 100 / total_tests ))%"
    
    if [ $failed_tests -eq 0 ]; then
        log "🎉 所有测试通过！备份系统运行正常"
        return 0
    else
        error "⚠️ 有 $failed_tests 个测试失败，请检查日志"
        return 1
    fi
}

# 显示帮助
show_help() {
    cat << EOF
OpenHands备份系统测试工具使用说明
================================

用法: $0 [选项]

选项:
  --all               运行所有测试
  --integrity PATH    测试指定备份的完整性
  --restore PATH      测试指定备份的恢复功能
  --performance       测试备份性能
  --protection        测试配置保护功能
  --health            测试系统健康检查
  --stress            执行压力测试
  --cleanup           清理测试环境
  -h, --help          显示此帮助信息

示例:
  $0 --all                                    # 运行所有测试
  $0 --integrity /backup/openhands/latest     # 测试备份完整性
  $0 --performance                            # 测试备份性能

注意事项:
- 测试过程中会创建临时文件和备份
- 某些测试可能需要较长时间
- 建议在维护窗口期间运行完整测试
EOF
}

# 主函数
main() {
    case "${1:-}" in
        --all)
            run_all_tests
            ;;
        --integrity)
            if [ -z "$2" ]; then
                error "请指定备份路径"
                exit 1
            fi
            test_backup_integrity "$2"
            ;;
        --restore)
            if [ -z "$2" ]; then
                error "请指定备份路径"
                exit 1
            fi
            test_restore_simulation "$2"
            ;;
        --performance)
            test_backup_performance
            ;;
        --protection)
            test_config_protection
            ;;
        --health)
            test_health_check
            ;;
        --stress)
            stress_test
            ;;
        --cleanup)
            cleanup_test_env
            ;;
        -h|--help)
            show_help
            ;;
        "")
            echo "请指定测试选项，使用 -h 查看帮助"
            show_help
            ;;
        *)
            error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

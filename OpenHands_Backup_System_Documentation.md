# OpenHands完整备份和回滚系统文档

## 📋 系统概述

OpenHands备份和回滚系统是一个完整的配置保护和恢复解决方案，确保系统配置的完整性和可恢复性。

### 🎯 主要目标
- **完整性保护**: 保护关键配置文件不被意外修改
- **快速恢复**: 提供一键回滚到稳定状态的能力
- **自动化监控**: 实时监控系统状态和配置变更
- **版本控制**: 维护配置的历史版本和变更记录

## 🏗️ 系统架构

### 核心组件

1. **备份系统** (`openhands_backup_system.sh`)
   - 完整系统备份
   - 增量备份支持
   - 自动清理旧备份

2. **回滚系统** (`openhands_rollback_system.sh`)
   - 交互式回滚
   - 快速回滚
   - 自动问题检测

3. **配置保护** (`openhands_config_protection.sh`)
   - 文件完整性监控
   - 只读保护
   - 实时告警

4. **测试验证** (`openhands_backup_test.sh`)
   - 备份完整性测试
   - 恢复功能验证
   - 性能测试

5. **管理界面** (`openhands_backup_manager.sh`)
   - 图形化管理界面
   - 状态监控
   - 日志查看

## 📁 目录结构

```
/backup/openhands/                    # 备份根目录
├── 20250804_165702/                  # 时间戳备份目录
│   ├── config/                       # 配置文件备份
│   ├── data/                         # 数据文件备份
│   ├── docker/                       # Docker配置备份
│   ├── nginx/                        # Nginx配置备份
│   ├── mcp/                          # MCP服务器备份
│   ├── logs/                         # 日志文件备份
│   ├── scripts/                      # 脚本文件备份
│   ├── restore.sh                    # 恢复脚本
│   ├── MANIFEST.txt                  # 备份清单
│   └── system_info.txt               # 系统信息
├── latest -> 20250804_165702/        # 最新备份链接
└── pre_rollback_*/                   # 回滚前备份

/etc/openhands-protection/            # 保护系统目录
├── checksums/                        # 文件校验和
├── readonly/                         # 只读保护记录
├── monitoring/                       # 监控配置
└── critical_files.txt                # 关键文件列表
```

## 🚀 快速开始

### 1. 系统初始化

```bash
# 设置脚本权限
chmod +x openhands_*.sh

# 初始化保护系统
./openhands_config_protection.sh --init

# 创建第一个备份
./openhands_backup_system.sh

# 生成配置校验和
./openhands_config_protection.sh --generate-checksums
```

### 2. 启动管理界面

```bash
sudo ./openhands_backup_manager.sh
```

### 3. 启动监控

```bash
./openhands_config_protection.sh --start-monitoring
```

## 🔧 详细使用说明

### 备份系统使用

#### 创建备份
```bash
# 创建完整备份
./openhands_backup_system.sh

# 备份将包含:
# - 系统配置文件
# - OpenHands用户数据
# - Docker容器配置
# - Nginx配置
# - MCP服务器代码
# - 日志文件
# - 管理脚本
```

#### 备份内容说明
- **配置文件**: OpenHands配置、Nginx配置、SSL证书
- **用户数据**: 对话历史、用户设置、工作空间
- **系统状态**: Docker容器配置、网络设置
- **自定义代码**: MCP服务器、脚本文件

### 回滚系统使用

#### 交互式回滚
```bash
./openhands_rollback_system.sh -i
```

#### 快速回滚到最新备份
```bash
./openhands_rollback_system.sh -q
```

#### 自动检测问题并建议回滚
```bash
./openhands_rollback_system.sh -a
```

#### 验证备份完整性
```bash
./openhands_rollback_system.sh -v /backup/openhands/20250804_165702
```

### 配置保护使用

#### 启用文件保护
```bash
# 设置关键文件为只读
./openhands_config_protection.sh --protect

# 移除只读保护
./openhands_config_protection.sh --unprotect
```

#### 监控配置变更
```bash
# 启动监控守护进程
./openhands_config_protection.sh --start-monitoring

# 停止监控
./openhands_config_protection.sh --stop-monitoring

# 手动验证完整性
./openhands_config_protection.sh --verify
```

#### 系统健康检查
```bash
./openhands_config_protection.sh --health-check
```

### 测试和验证

#### 运行完整测试
```bash
./openhands_backup_test.sh --all
```

#### 单项测试
```bash
# 备份性能测试
./openhands_backup_test.sh --performance

# 配置保护测试
./openhands_backup_test.sh --protection

# 健康检查测试
./openhands_backup_test.sh --health
```

## ⚙️ 配置说明

### 监控配置
文件位置: `/etc/openhands-protection/monitoring/config.conf`

```bash
CHECK_INTERVAL=300      # 检查间隔（秒）
ALERT_EMAIL=""          # 告警邮箱
AUTO_BACKUP=true        # 自动备份变更
AUTO_ROLLBACK=false     # 自动回滚（谨慎启用）
```

### 关键文件列表
文件位置: `/etc/openhands-protection/critical_files.txt`

```
/etc/nginx/sites-available/ai.guiyunai.fun
/etc/nginx/nginx.conf
/www/wwwroot/ai.guiyunai.fun/OpenHands/config.toml
/www/wwwroot/ai.guiyunai.fun/start_openhands_optimized.sh
/www/wwwroot/ai.guiyunai.fun/mcp_servers/fengshui_server.py
/www/wwwroot/ai.guiyunai.fun/mcp_servers/yijing_server.py
/www/wwwroot/ai.guiyunai.fun/.openhands/settings.json
```

## 🔄 自动化设置

### 定时备份
```bash
# 每日凌晨2点自动备份
0 2 * * * cd /www/wwwroot/ai.guiyunai.fun && ./openhands_backup_system.sh

# 每小时健康检查
0 * * * * cd /www/wwwroot/ai.guiyunai.fun && ./openhands_config_protection.sh --health-check
```

### 监控告警
系统支持以下告警方式：
- 日志记录
- 邮件通知（需配置SMTP）
- 自动备份触发
- 自动回滚（可选）

## 🚨 故障排除

### 常见问题

#### 1. 备份失败
**症状**: 备份脚本执行失败
**解决方案**:
```bash
# 检查磁盘空间
df -h

# 检查权限
ls -la /backup/openhands/

# 查看详细错误
./openhands_backup_system.sh 2>&1 | tee backup_debug.log
```

#### 2. 回滚失败
**症状**: 回滚过程中出现错误
**解决方案**:
```bash
# 验证备份完整性
./openhands_rollback_system.sh -v /backup/openhands/latest

# 检查服务状态
docker ps
systemctl status nginx

# 手动恢复关键服务
docker restart openhands-app
```

#### 3. 监控进程异常
**症状**: 配置监控停止工作
**解决方案**:
```bash
# 检查进程状态
ps aux | grep openhands_config_protection

# 重启监控
./openhands_config_protection.sh --stop-monitoring
./openhands_config_protection.sh --start-monitoring

# 检查日志
tail -f /var/log/openhands_protection.log
```

## 📊 性能优化

### 备份优化
- 使用压缩减少存储空间
- 排除不必要的文件（node_modules、缓存等）
- 定期清理旧备份

### 监控优化
- 调整检查间隔平衡性能和实时性
- 使用文件系统事件而非轮询（高级配置）
- 优化校验和算法

## 🔒 安全考虑

### 访问控制
- 备份文件存储在受保护的目录
- 脚本需要root权限执行
- 敏感信息加密存储

### 数据保护
- 备份文件完整性校验
- 传输过程加密（如果远程备份）
- 访问日志记录

## 📈 监控指标

### 系统健康指标
- OpenHands容器状态
- HTTP服务响应
- 磁盘空间使用
- 内存使用率
- 配置文件完整性

### 备份指标
- 备份成功率
- 备份大小趋势
- 备份耗时
- 存储空间使用

## 🔮 未来规划

### 计划功能
- 远程备份支持
- 增量备份优化
- Web管理界面
- 集群备份支持
- 云存储集成

### 改进方向
- 性能优化
- 用户体验改善
- 更多监控指标
- 智能告警

---

## 📞 支持和维护

### 联系信息
- 维护团队: 系统管理员
- 更新频率: 定期更新
- 文档版本: 2.0

### 更新日志
- 2025-08-04: 初始版本发布
- 包含完整的备份、回滚、保护功能
- 支持自动化监控和测试验证

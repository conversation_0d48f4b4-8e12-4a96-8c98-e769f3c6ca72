# 中国传统文化OpenHands平台部署指南

## 🏮 项目概述

本指南将帮助您将OpenHands平台改造为专业的中国传统文化AI助手，集成易经、风水、中医、非遗等领域的专业知识。

## 🎯 核心功能模块

### 1. 易经八卦智能分析
- 六十四卦查询与解释
- 占卜结果智能分析
- 爻辞变化解读
- 人生哲学指导

### 2. 风水堪舆专业咨询
- 住宅风水分析
- 办公室布局优化
- 五行平衡计算
- 方位吉凶判断

### 3. 中医药典知识库
- 中药材查询
- 方剂配伍分析
- 症状诊断辅助
- 体质分析建议

### 4. 非遗文化传承
- UNESCO非遗项目查询
- 传统工艺教学
- 民俗文化解释
- 节庆习俗介绍

## 🔧 技术架构

### MCP服务器架构
```
OpenHands Core
    ├── 易经知识库MCP (yijing_server.py)
    ├── 风水分析MCP (fengshui_server.py)
    ├── 中医药典MCP (tcm_server.py)
    ├── 非遗文化MCP (heritage_server.py)
    ├── 传统历法MCP (calendar_server.py)
    ├── 古典文学MCP (literature_server.py)
    ├── 堪舆工具MCP (geomancy_server.py)
    ├── 八字命理MCP (bazi_server.py)
    ├── 中医诊断MCP (tcm_diagnosis_server.py)
    └── 文化背景MCP (cultural_context_server.py)
```

## 📦 部署步骤

### 第一步：准备知识库数据

1. **创建数据目录结构**
```bash
mkdir -p /data/{yijing,fengshui,tcm,heritage,calendar,literature,geomancy,bazi,culture}
mkdir -p /data/knowledge/{yijing,fengshui,tcm,heritage}
```

2. **下载和整理知识库**
```bash
# 易经数据
wget -O /data/yijing/hexagrams.json "https://raw.githubusercontent.com/chinese-culture/yijing-data/main/hexagrams.json"

# 风水规则
wget -O /data/fengshui/rules.json "https://raw.githubusercontent.com/chinese-culture/fengshui-data/main/rules.json"

# 中医药典
wget -O /data/tcm/herbs.json "https://raw.githubusercontent.com/chinese-culture/tcm-data/main/herbs.json"

# 非遗项目
wget -O /data/heritage/unesco_china.json "https://raw.githubusercontent.com/chinese-culture/heritage-data/main/unesco.json"
```

### 第二步：安装MCP服务器依赖

```bash
cd /www/wwwroot/ai.guiyunai.fun/OpenHands

# 安装MCP相关依赖
pip install mcp sqlite3 jieba pypinyin

# 创建MCP服务器目录
mkdir -p mcp_servers
```

### 第三步：部署MCP服务器

1. **复制服务器文件**
```bash
# 将之前创建的MCP服务器文件复制到正确位置
cp mcp_servers/*.py /www/wwwroot/ai.guiyunai.fun/OpenHands/mcp_servers/
```

2. **配置OpenHands MCP设置**
```bash
# 编辑OpenHands配置文件
nano /www/wwwroot/ai.guiyunai.fun/OpenHands/config.toml
```

添加MCP配置：
```toml
[mcp]
servers = [
    {
        name = "yijing-knowledge"
        command = "python"
        args = ["-m", "mcp_servers.yijing_server"]
        env = { YIJING_DB_PATH = "/data/yijing/knowledge.db", LANGUAGE = "zh-CN" }
    },
    {
        name = "fengshui-analysis" 
        command = "python"
        args = ["-m", "mcp_servers.fengshui_server"]
        env = { FENGSHUI_RULES_PATH = "/data/fengshui/rules.json", LANGUAGE = "zh-CN" }
    }
]
```

### 第四步：配置AI模型优化

1. **优化中文理解**
```toml
[llm]
model = "groq/llama-3.3-70b-versatile"
api_key = "********************************************************"
system_prompt = """你是一位精通中国传统文化的AI助手，专门帮助用户学习和理解：
- 易经八卦和占卜分析
- 风水堪舆和环境布局
- 中医药理和养生保健
- 非物质文化遗产和传统工艺
- 古典文学和哲学思想

请用专业而易懂的中文回答问题，结合传统智慧和现代应用。"""

# 备用模型配置
[llm.backup]
model = "deepseek/deepseek-coder"
api_key = "***********************************"
```

### 第五步：界面本地化

1. **设置中文界面**
```bash
# 修改前端配置，默认使用中文
cd /www/wwwroot/ai.guiyunai.fun/OpenHands/frontend
nano src/i18n/index.ts
```

2. **添加传统文化主题**
```css
/* 在 src/index.css 中添加中国风主题 */
.chinese-culture-theme {
    --primary-color: #8B4513;
    --secondary-color: #DAA520;
    --accent-color: #DC143C;
    --background-color: #FFF8DC;
    font-family: "思源宋体", "Source Han Serif", serif;
}
```

### 第六步：数据库初始化

```bash
# 运行数据库初始化脚本
cd /www/wwwroot/ai.guiyunai.fun/OpenHands
python -c "
from mcp_servers.yijing_server import YijingKnowledgeServer
from mcp_servers.fengshui_server import FengshuiAnalysisServer

# 初始化数据库
yijing_server = YijingKnowledgeServer()
fengshui_server = FengshuiAnalysisServer()
print('数据库初始化完成')
"
```

## 🎨 界面定制

### 1. 添加传统文化元素

```javascript
// 在前端添加传统文化图标和装饰
const CulturalIcons = {
    yijing: "☯",
    fengshui: "🏮", 
    tcm: "🌿",
    heritage: "🏛️"
};
```

### 2. 自定义聊天界面

```jsx
// 添加专业领域快捷按钮
const CulturalQuickActions = () => (
    <div className="cultural-actions">
        <button onClick={() => startYijingConsultation()}>易经占卜</button>
        <button onClick={() => startFengshuiAnalysis()}>风水分析</button>
        <button onClick={() => startTCMConsultation()}>中医咨询</button>
        <button onClick={() => exploreHeritage()}>非遗探索</button>
    </div>
);
```

## 🔍 功能验证

### 1. 测试易经功能
```bash
curl -X POST https://ai.guiyunai.fun/api/mcp/yijing-knowledge/hexagram_query \
  -H "Content-Type: application/json" \
  -d '{"query": "乾卦", "method": "name"}'
```

### 2. 测试风水功能
```bash
curl -X POST https://ai.guiyunai.fun/api/mcp/fengshui-analysis/house_analysis \
  -H "Content-Type: application/json" \
  -d '{"house_direction": "正南", "door_direction": "东南", "layout_description": "三室两厅"}'
```

## 📊 性能优化

### 1. 知识库索引优化
```sql
-- 为常用查询创建索引
CREATE INDEX idx_hexagram_name ON hexagrams(name_chinese);
CREATE INDEX idx_direction_fortune ON direction_fortune(direction);
CREATE INDEX idx_herb_name ON herbs(name_chinese);
```

### 2. 缓存策略
```python
# 实现知识库查询缓存
import redis
redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cached_query(key, query_func):
    cached_result = redis_client.get(key)
    if cached_result:
        return json.loads(cached_result)
    
    result = query_func()
    redis_client.setex(key, 3600, json.dumps(result))
    return result
```

## 🛡️ 安全考虑

### 1. 数据验证
```python
def validate_cultural_query(query):
    # 验证查询内容的合法性
    forbidden_patterns = ["政治", "敏感", "迷信"]
    return not any(pattern in query for pattern in forbidden_patterns)
```

### 2. 访问控制
```python
# 实现专业功能的访问控制
def require_cultural_expertise(func):
    def wrapper(*args, **kwargs):
        if not user_has_cultural_access():
            return {"error": "需要传统文化专业权限"}
        return func(*args, **kwargs)
    return wrapper
```

## 📈 监控和维护

### 1. 使用统计
```python
# 记录各功能模块的使用情况
def log_cultural_usage(module, action, user_id):
    usage_log = {
        "timestamp": datetime.now(),
        "module": module,
        "action": action,
        "user_id": user_id
    }
    # 保存到数据库或日志文件
```

### 2. 知识库更新
```bash
# 定期更新知识库脚本
#!/bin/bash
cd /data/knowledge
git pull origin main
python update_databases.py
systemctl restart openhands-mcp-servers
```

## 🎓 用户指南

### 易经咨询示例
```
用户：我想问事业发展，请帮我占一卦
AI：好的，我来为您进行易经占卜分析。请您心中默念您的问题，然后告诉我一个1-64之间的数字，或者描述您当前的具体情况。
```

### 风水分析示例
```
用户：我家客厅朝南，沙发应该怎么摆放？
AI：根据您家客厅朝南的情况，我来为您分析最佳的沙发摆放方案...
```

## 🔮 未来扩展

1. **增加更多传统文化模块**
   - 面相手相分析
   - 姓名学分析
   - 择日择时功能

2. **集成现代科技**
   - AR风水罗盘
   - 语音识别中医问诊
   - 图像识别草药识别

3. **社区功能**
   - 传统文化学习社区
   - 专家在线咨询
   - 文化传承项目

通过以上配置，您的OpenHands平台将成为一个专业的中国传统文化AI助手，为用户提供权威、专业的传统文化知识服务。

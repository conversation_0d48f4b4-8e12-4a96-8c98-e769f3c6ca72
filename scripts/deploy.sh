#!/bin/bash

# OpenHands 部署脚本
# 用于快速部署和恢复OpenHands服务

set -e

echo "🚀 OpenHands 部署脚本"
echo "===================="

# 配置变量
CONTAINER_NAME="openhands-app"
IMAGE_NAME="docker.all-hands.dev/all-hands-ai/openhands:0.51"
RUNTIME_IMAGE="docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik"
OPENHANDS_DIR="$HOME/.openhands"

# 函数：停止并清理现有容器
cleanup_containers() {
    echo "🧹 清理现有容器..."
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    
    # 清理运行时容器
    docker stop $(docker ps -q --filter "name=openhands-runtime") 2>/dev/null || true
    docker rm $(docker ps -aq --filter "name=openhands-runtime") 2>/dev/null || true
}

# 函数：拉取最新镜像
pull_images() {
    echo "📥 拉取Docker镜像..."
    docker pull $RUNTIME_IMAGE
    docker pull $IMAGE_NAME
}

# 函数：启动OpenHands
start_openhands() {
    echo "🚀 启动OpenHands容器..."
    docker run -it --rm --pull=never \
        -e SANDBOX_RUNTIME_CONTAINER_IMAGE=$RUNTIME_IMAGE \
        -e LOG_ALL_EVENTS=true \
        -v /var/run/docker.sock:/var/run/docker.sock \
        -v $OPENHANDS_DIR:/.openhands \
        -p 3000:3000 \
        --add-host host.docker.internal:host-gateway \
        --name $CONTAINER_NAME \
        $IMAGE_NAME &
    
    echo "⏳ 等待服务启动..."
    sleep 15
    
    if docker ps | grep -q $CONTAINER_NAME; then
        echo "✅ OpenHands 启动成功！"
        echo "🌐 访问地址: https://ai.guiyunai.fun"
    else
        echo "❌ OpenHands 启动失败！"
        exit 1
    fi
}

# 函数：检查设置文件
check_settings() {
    if [ ! -f "$OPENHANDS_DIR/settings.json" ]; then
        echo "⚠️  设置文件不存在，请从模板复制："
        echo "cp config-backup/settings-template.json $OPENHANDS_DIR/settings.json"
        echo "然后编辑文件添加你的API密钥"
        return 1
    fi
    
    if grep -q "YOUR_DEEPSEEK_API_KEY_HERE" "$OPENHANDS_DIR/settings.json"; then
        echo "⚠️  请在设置文件中配置你的DeepSeek API密钥"
        return 1
    fi
    
    echo "✅ 设置文件检查通过"
    return 0
}

# 主执行流程
case "${1:-deploy}" in
    "deploy")
        echo "📋 执行完整部署..."
        cleanup_containers
        pull_images
        check_settings || echo "请手动配置设置文件"
        start_openhands
        ;;
    "restart")
        echo "🔄 重启服务..."
        cleanup_containers
        start_openhands
        ;;
    "stop")
        echo "🛑 停止服务..."
        cleanup_containers
        echo "✅ 服务已停止"
        ;;
    "status")
        echo "📊 检查服务状态..."
        if docker ps | grep -q $CONTAINER_NAME; then
            echo "✅ OpenHands 正在运行"
            docker ps | grep $CONTAINER_NAME
        else
            echo "❌ OpenHands 未运行"
        fi
        ;;
    *)
        echo "用法: $0 {deploy|restart|stop|status}"
        echo "  deploy  - 完整部署（默认）"
        echo "  restart - 重启服务"
        echo "  stop    - 停止服务"
        echo "  status  - 检查状态"
        exit 1
        ;;
esac

echo "🎉 操作完成！"

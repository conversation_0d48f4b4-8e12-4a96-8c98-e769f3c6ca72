#!/bin/bash

# OpenHands 备份脚本
# 用于备份配置和数据

set -e

echo "💾 OpenHands 备份脚本"
echo "==================="

# 配置变量
BACKUP_DIR="backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="openhands_backup_$TIMESTAMP"
OPENHANDS_DIR="$HOME/.openhands"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 函数：备份配置文件
backup_config() {
    echo "📁 备份配置文件..."
    
    if [ -f "$OPENHANDS_DIR/settings.json" ]; then
        # 创建安全的配置备份（移除敏感信息）
        cp "$OPENHANDS_DIR/settings.json" "$BACKUP_DIR/${BACKUP_NAME}_settings.json"
        
        # 创建模板版本（移除API密钥）
        sed 's/"llm_api_key":"[^"]*"/"llm_api_key":"YOUR_DEEPSEEK_API_KEY_HERE"/g' \
            "$OPENHANDS_DIR/settings.json" > "$BACKUP_DIR/${BACKUP_NAME}_settings_template.json"
        
        echo "✅ 配置文件已备份"
    else
        echo "⚠️  配置文件不存在"
    fi
}

# 函数：备份会话数据
backup_sessions() {
    echo "💬 备份会话数据..."
    
    if [ -d "$OPENHANDS_DIR/sessions" ]; then
        tar -czf "$BACKUP_DIR/${BACKUP_NAME}_sessions.tar.gz" -C "$OPENHANDS_DIR" sessions/
        echo "✅ 会话数据已备份"
    else
        echo "⚠️  会话数据不存在"
    fi
}

# 函数：备份Docker镜像信息
backup_docker_info() {
    echo "🐳 备份Docker信息..."
    
    docker images | grep openhands > "$BACKUP_DIR/${BACKUP_NAME}_docker_images.txt"
    docker ps -a | grep openhands > "$BACKUP_DIR/${BACKUP_NAME}_docker_containers.txt" || true
    
    echo "✅ Docker信息已备份"
}

# 函数：创建恢复脚本
create_restore_script() {
    echo "🔄 创建恢复脚本..."
    
    cat > "$BACKUP_DIR/${BACKUP_NAME}_restore.sh" << EOF
#!/bin/bash

# OpenHands 恢复脚本 - $TIMESTAMP
# 用于恢复到备份时的状态

set -e

echo "🔄 恢复OpenHands到 $TIMESTAMP 状态"
echo "=================================="

OPENHANDS_DIR="\$HOME/.openhands"
BACKUP_DIR="\$(dirname "\$0")"

# 停止现有服务
echo "🛑 停止现有服务..."
docker stop openhands-app 2>/dev/null || true
docker rm openhands-app 2>/dev/null || true

# 备份当前配置
if [ -f "\$OPENHANDS_DIR/settings.json" ]; then
    cp "\$OPENHANDS_DIR/settings.json" "\$OPENHANDS_DIR/settings.json.backup.\$(date +%Y%m%d_%H%M%S)"
    echo "✅ 当前配置已备份"
fi

# 恢复配置文件
if [ -f "\$BACKUP_DIR/${BACKUP_NAME}_settings.json" ]; then
    mkdir -p "\$OPENHANDS_DIR"
    cp "\$BACKUP_DIR/${BACKUP_NAME}_settings.json" "\$OPENHANDS_DIR/settings.json"
    echo "✅ 配置文件已恢复"
    
    # 提醒用户检查API密钥
    if grep -q "YOUR_DEEPSEEK_API_KEY_HERE" "\$OPENHANDS_DIR/settings.json"; then
        echo "⚠️  请更新API密钥在: \$OPENHANDS_DIR/settings.json"
    fi
else
    echo "❌ 配置备份文件不存在"
fi

# 恢复会话数据
if [ -f "\$BACKUP_DIR/${BACKUP_NAME}_sessions.tar.gz" ]; then
    tar -xzf "\$BACKUP_DIR/${BACKUP_NAME}_sessions.tar.gz" -C "\$OPENHANDS_DIR/"
    echo "✅ 会话数据已恢复"
fi

# 启动服务
echo "🚀 启动OpenHands服务..."
cd /www/wwwroot/ai.guiyunai.fun
./scripts/deploy.sh restart

echo "🎉 恢复完成！"
echo "🌐 访问地址: https://ai.guiyunai.fun"
EOF

    chmod +x "$BACKUP_DIR/${BACKUP_NAME}_restore.sh"
    echo "✅ 恢复脚本已创建: $BACKUP_DIR/${BACKUP_NAME}_restore.sh"
}

# 函数：创建备份信息文件
create_backup_info() {
    cat > "$BACKUP_DIR/${BACKUP_NAME}_info.txt" << EOF
OpenHands 备份信息
================

备份时间: $TIMESTAMP
备份位置: $(pwd)/$BACKUP_DIR
系统信息: $(uname -a)
Docker版本: $(docker --version)

包含文件:
- ${BACKUP_NAME}_settings.json (完整配置)
- ${BACKUP_NAME}_settings_template.json (模板配置)
- ${BACKUP_NAME}_sessions.tar.gz (会话数据)
- ${BACKUP_NAME}_docker_images.txt (Docker镜像信息)
- ${BACKUP_NAME}_docker_containers.txt (Docker容器信息)
- ${BACKUP_NAME}_restore.sh (恢复脚本)

恢复方法:
1. 运行恢复脚本: ./${BACKUP_NAME}_restore.sh
2. 或手动复制配置文件到 ~/.openhands/settings.json
3. 运行部署脚本: ./scripts/deploy.sh restart

注意事项:
- 请确保API密钥已正确配置
- 恢复前会自动备份当前配置
- 如有问题请检查Docker服务状态
EOF

    echo "✅ 备份信息已创建: $BACKUP_DIR/${BACKUP_NAME}_info.txt"
}

# 主执行流程
echo "📅 备份时间: $TIMESTAMP"
echo "📁 备份目录: $BACKUP_DIR"

backup_config
backup_sessions
backup_docker_info
create_restore_script
create_backup_info

echo ""
echo "🎉 备份完成！"
echo "📁 备份文件位置: $BACKUP_DIR/"
echo "🔄 恢复命令: ./$BACKUP_DIR/${BACKUP_NAME}_restore.sh"
echo ""
echo "📋 备份包含:"
ls -la "$BACKUP_DIR/${BACKUP_NAME}"*

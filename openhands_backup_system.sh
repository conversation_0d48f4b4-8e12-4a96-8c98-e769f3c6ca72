#!/bin/bash

# OpenHands完整备份系统
# 版本: 2.0
# 作者: 系统管理员
# 日期: 2025-08-04

set -e  # 遇到错误立即退出

# 配置变量
BACKUP_BASE_DIR="/backup/openhands"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="${BACKUP_BASE_DIR}/${TIMESTAMP}"
PROJECT_DIR="/www/wwwroot/ai.guiyunai.fun"
LOG_FILE="${BACKUP_DIR}/backup.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

# 创建备份目录
create_backup_dirs() {
    echo "创建备份目录结构..."
    mkdir -p "${BACKUP_DIR}"/{config,data,docker,nginx,mcp,logs,scripts}
    mkdir -p "${BACKUP_BASE_DIR}/latest"
    touch "$LOG_FILE"
    log "创建备份目录结构..."
}

# 备份系统信息
backup_system_info() {
    log "备份系统信息..."
    
    # 系统状态
    {
        echo "=== 系统信息 ==="
        echo "备份时间: $(date)"
        echo "系统版本: $(cat /etc/os-release | grep PRETTY_NAME)"
        echo "内核版本: $(uname -r)"
        echo "Docker版本: $(docker --version)"
        echo ""
        
        echo "=== 系统资源 ==="
        echo "内存使用:"
        free -h
        echo ""
        echo "磁盘使用:"
        df -h
        echo ""
        
        echo "=== 网络配置 ==="
        ip addr show
        echo ""
        
        echo "=== 进程状态 ==="
        ps aux | grep -E "(nginx|docker|openhands)" | grep -v grep
        
    } > "${BACKUP_DIR}/system_info.txt"
}

# 备份Docker相关
backup_docker() {
    log "备份Docker配置和状态..."
    
    # Docker容器状态
    docker ps -a > "${BACKUP_DIR}/docker/containers.txt"
    docker images > "${BACKUP_DIR}/docker/images.txt"
    docker network ls > "${BACKUP_DIR}/docker/networks.txt"
    docker volume ls > "${BACKUP_DIR}/docker/volumes.txt"
    
    # Docker Compose文件
    if [ -f "${PROJECT_DIR}/docker-compose.yml" ]; then
        cp "${PROJECT_DIR}/docker-compose.yml" "${BACKUP_DIR}/docker/"
    fi
    
    if [ -f "${PROJECT_DIR}/docker-compose.override.yml" ]; then
        cp "${PROJECT_DIR}/docker-compose.override.yml" "${BACKUP_DIR}/docker/"
    fi
    
    # 导出OpenHands镜像配置
    docker inspect openhands-app > "${BACKUP_DIR}/docker/openhands_container_config.json" 2>/dev/null || true
    
    # 备份运行时容器配置
    RUNTIME_CONTAINERS=$(docker ps | grep runtime | awk '{print $1}')
    if [ ! -z "$RUNTIME_CONTAINERS" ]; then
        echo "$RUNTIME_CONTAINERS" | while read container_id; do
            docker inspect "$container_id" > "${BACKUP_DIR}/docker/runtime_${container_id}_config.json"
        done
    fi
}

# 备份Nginx配置
backup_nginx() {
    log "备份Nginx配置..."
    
    # 主配置文件
    cp /etc/nginx/nginx.conf "${BACKUP_DIR}/nginx/"
    
    # 站点配置
    cp -r /etc/nginx/sites-available "${BACKUP_DIR}/nginx/"
    cp -r /etc/nginx/sites-enabled "${BACKUP_DIR}/nginx/"
    
    # SSL证书信息
    if [ -d "/etc/letsencrypt" ]; then
        cp -r /etc/letsencrypt "${BACKUP_DIR}/nginx/"
    fi
    
    # 测试Nginx配置
    nginx -t > "${BACKUP_DIR}/nginx/config_test.txt" 2>&1 || true
}

# 备份OpenHands配置和数据
backup_openhands_data() {
    log "备份OpenHands配置和数据..."
    
    # 配置文件
    if [ -f "${PROJECT_DIR}/OpenHands/config.toml" ]; then
        cp "${PROJECT_DIR}/OpenHands/config.toml" "${BACKUP_DIR}/config/"
    fi
    
    # 用户数据和会话
    if [ -d "${PROJECT_DIR}/.openhands" ]; then
        tar -czf "${BACKUP_DIR}/data/openhands_user_data.tar.gz" -C "${PROJECT_DIR}" .openhands
    fi
    
    # 工作空间数据
    if [ -d "${PROJECT_DIR}/workspace" ]; then
        tar -czf "${BACKUP_DIR}/data/workspace.tar.gz" -C "${PROJECT_DIR}" workspace
    fi
    
    # 项目文件（排除大文件和临时文件）
    tar -czf "${BACKUP_DIR}/data/project_files.tar.gz" \
        --exclude="node_modules" \
        --exclude="__pycache__" \
        --exclude="*.pyc" \
        --exclude=".git" \
        --exclude="logs" \
        --exclude="cache" \
        -C "${PROJECT_DIR}" .
}

# 备份MCP服务器
backup_mcp_servers() {
    log "备份MCP服务器配置..."
    
    # MCP服务器代码
    if [ -d "${PROJECT_DIR}/mcp_servers" ]; then
        cp -r "${PROJECT_DIR}/mcp_servers" "${BACKUP_DIR}/mcp/"
    fi
    
    # MCP配置文件
    find "${PROJECT_DIR}" -name "*mcp*.json" -exec cp {} "${BACKUP_DIR}/mcp/" \;
    
    # MCP代理配置
    if [ -d "${PROJECT_DIR}/mcp-proxies" ]; then
        cp -r "${PROJECT_DIR}/mcp-proxies" "${BACKUP_DIR}/mcp/"
    fi
    
    # 数据文件
    if [ -d "${PROJECT_DIR}/data" ]; then
        cp -r "${PROJECT_DIR}/data" "${BACKUP_DIR}/mcp/"
    fi
}

# 备份日志文件
backup_logs() {
    log "备份日志文件..."
    
    # Nginx日志
    if [ -d "/var/log/nginx" ]; then
        tar -czf "${BACKUP_DIR}/logs/nginx_logs.tar.gz" -C /var/log nginx
    fi
    
    # Docker日志
    docker logs openhands-app > "${BACKUP_DIR}/logs/openhands_container.log" 2>&1 || true
    
    # 系统日志
    journalctl -u docker --since "1 day ago" > "${BACKUP_DIR}/logs/docker_service.log" 2>/dev/null || true
    
    # 项目日志
    if [ -d "${PROJECT_DIR}/logs" ]; then
        cp -r "${PROJECT_DIR}/logs" "${BACKUP_DIR}/logs/project_logs"
    fi
}

# 备份脚本文件
backup_scripts() {
    log "备份管理脚本..."
    
    # 复制所有shell脚本
    find "${PROJECT_DIR}" -name "*.sh" -exec cp {} "${BACKUP_DIR}/scripts/" \;
    
    # 复制Python脚本
    find "${PROJECT_DIR}" -maxdepth 1 -name "*.py" -exec cp {} "${BACKUP_DIR}/scripts/" \;
    
    # 复制配置文件
    find "${PROJECT_DIR}" -maxdepth 1 -name "*.json" -exec cp {} "${BACKUP_DIR}/scripts/" \;
    find "${PROJECT_DIR}" -maxdepth 1 -name "*.md" -exec cp {} "${BACKUP_DIR}/scripts/" \;
}

# 创建恢复脚本
create_restore_script() {
    log "创建恢复脚本..."
    
    cat > "${BACKUP_DIR}/restore.sh" << 'EOF'
#!/bin/bash

# OpenHands系统恢复脚本
# 自动生成于备份时

set -e

BACKUP_DIR="$(dirname "$0")"
PROJECT_DIR="/www/wwwroot/ai.guiyunai.fun"

echo "🔄 开始恢复OpenHands系统..."
echo "备份目录: $BACKUP_DIR"
echo "项目目录: $PROJECT_DIR"

# 停止服务
echo "停止OpenHands服务..."
docker stop openhands-app 2>/dev/null || true
systemctl stop nginx 2>/dev/null || true

# 恢复Nginx配置
echo "恢复Nginx配置..."
if [ -d "$BACKUP_DIR/nginx/sites-available" ]; then
    cp -r "$BACKUP_DIR/nginx/sites-available"/* /etc/nginx/sites-available/
fi

if [ -d "$BACKUP_DIR/nginx/sites-enabled" ]; then
    cp -r "$BACKUP_DIR/nginx/sites-enabled"/* /etc/nginx/sites-enabled/
fi

# 恢复OpenHands数据
echo "恢复OpenHands数据..."
if [ -f "$BACKUP_DIR/data/openhands_user_data.tar.gz" ]; then
    tar -xzf "$BACKUP_DIR/data/openhands_user_data.tar.gz" -C "$PROJECT_DIR"
fi

if [ -f "$BACKUP_DIR/data/workspace.tar.gz" ]; then
    tar -xzf "$BACKUP_DIR/data/workspace.tar.gz" -C "$PROJECT_DIR"
fi

# 恢复配置文件
echo "恢复配置文件..."
if [ -f "$BACKUP_DIR/config/config.toml" ]; then
    cp "$BACKUP_DIR/config/config.toml" "$PROJECT_DIR/OpenHands/"
fi

# 恢复MCP服务器
echo "恢复MCP服务器..."
if [ -d "$BACKUP_DIR/mcp/mcp_servers" ]; then
    cp -r "$BACKUP_DIR/mcp/mcp_servers" "$PROJECT_DIR/"
fi

# 恢复脚本
echo "恢复管理脚本..."
if [ -d "$BACKUP_DIR/scripts" ]; then
    find "$BACKUP_DIR/scripts" -name "*.sh" -exec cp {} "$PROJECT_DIR/" \;
fi

# 重启服务
echo "重启服务..."
nginx -t && systemctl start nginx
cd "$PROJECT_DIR" && ./start_openhands_optimized.sh

echo "✅ 恢复完成！"
EOF

    chmod +x "${BACKUP_DIR}/restore.sh"
}

# 创建备份清单
create_manifest() {
    log "创建备份清单..."
    
    {
        echo "OpenHands系统备份清单"
        echo "====================="
        echo "备份时间: $(date)"
        echo "备份版本: $TIMESTAMP"
        echo "备份大小: $(du -sh "$BACKUP_DIR" | cut -f1)"
        echo ""
        echo "备份内容:"
        echo "- 系统信息和状态"
        echo "- Docker容器和镜像配置"
        echo "- Nginx配置和SSL证书"
        echo "- OpenHands用户数据和会话"
        echo "- 工作空间数据"
        echo "- MCP服务器配置和代码"
        echo "- 日志文件"
        echo "- 管理脚本"
        echo ""
        echo "文件清单:"
        find "$BACKUP_DIR" -type f | sort
        
    } > "${BACKUP_DIR}/MANIFEST.txt"
}

# 更新最新备份链接
update_latest_link() {
    log "更新最新备份链接..."
    
    rm -rf "${BACKUP_BASE_DIR}/latest"
    ln -s "$BACKUP_DIR" "${BACKUP_BASE_DIR}/latest"
    
    # 创建快速访问脚本
    cat > "${BACKUP_BASE_DIR}/latest/quick_restore.sh" << EOF
#!/bin/bash
echo "快速恢复到备份版本: $TIMESTAMP"
echo "执行: bash restore.sh"
EOF
    chmod +x "${BACKUP_BASE_DIR}/latest/quick_restore.sh"
}

# 清理旧备份
cleanup_old_backups() {
    log "清理旧备份（保留最近10个）..."
    
    cd "$BACKUP_BASE_DIR"
    ls -1t | grep -E '^[0-9]{8}_[0-9]{6}$' | tail -n +11 | xargs -r rm -rf
}

# 主函数
main() {
    echo "🚀 OpenHands完整备份系统启动"
    echo "================================"
    
    create_backup_dirs
    
    log "开始备份过程..."
    backup_system_info
    backup_docker
    backup_nginx
    backup_openhands_data
    backup_mcp_servers
    backup_logs
    backup_scripts
    
    create_restore_script
    create_manifest
    update_latest_link
    cleanup_old_backups
    
    log "备份完成！"
    echo ""
    echo "📊 备份统计:"
    echo "- 备份位置: $BACKUP_DIR"
    echo "- 备份大小: $(du -sh "$BACKUP_DIR" | cut -f1)"
    echo "- 文件数量: $(find "$BACKUP_DIR" -type f | wc -l)"
    echo ""
    echo "🔄 恢复方法:"
    echo "cd $BACKUP_DIR && bash restore.sh"
    echo ""
    echo "📋 快速访问:"
    echo "最新备份: ${BACKUP_BASE_DIR}/latest"
}

# 执行主函数
main "$@"

#!/bin/bash

# OpenHands MCP服务器状态检查脚本
# 版本: 1.0
# 日期: 2025-08-04

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 OpenHands MCP服务器状态检查${NC}"
echo "=================================="

# 检查OpenHands服务状态
echo -e "\n${YELLOW}🌐 OpenHands服务状态:${NC}"
if docker ps | grep -q openhands-app; then
    echo -e "✅ OpenHands容器: ${GREEN}运行中${NC}"
    
    # 检查HTTP服务
    http_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
    if [ "$http_code" = "200" ]; then
        echo -e "✅ HTTP服务: ${GREEN}正常 (HTTP $http_code)${NC}"
    else
        echo -e "❌ HTTP服务: ${RED}异常 (HTTP $http_code)${NC}"
    fi
    
    # 检查域名访问
    domain_code=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null)
    if [ "$domain_code" = "200" ]; then
        echo -e "✅ 域名访问: ${GREEN}正常 (HTTP $domain_code)${NC}"
    else
        echo -e "⚠️ 域名访问: ${YELLOW}异常 (HTTP $domain_code)${NC}"
    fi
else
    echo -e "❌ OpenHands容器: ${RED}未运行${NC}"
fi

# 检查MCP代理服务器状态
echo -e "\n${YELLOW}🔧 MCP代理服务器状态:${NC}"

ports=(8080 8081 8082 8084 8090 8100 8101 8110)
names=("文件系统" "内存" "网页获取" "Git" "营销推广" "剧本杀" "设计创意" "编程开发")
categories=("官方" "官方" "官方" "官方" "营销" "创作" "创作" "职业")

for i in "${!ports[@]}"; do
    port=${ports[$i]}
    name=${names[$i]}
    category=${categories[$i]}
    
    echo -n "[$category] $name 服务器 (端口 $port): "
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        # 端口被占用，检查是否可以连接
        if curl -s --max-time 3 http://localhost:$port/sse >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 运行正常${NC}"
        else
            echo -e "${YELLOW}⚠️ 端口占用但连接失败${NC}"
        fi
    else
        echo -e "${RED}❌ 未运行${NC}"
    fi
done

# 检查MCP代理进程
echo -e "\n${YELLOW}🔄 MCP代理进程状态:${NC}"

# 检查官方MCP代理进程
if [ -d "/www/wwwroot/ai.guiyunai.fun/mcp-proxies/official/pids" ]; then
    echo "官方MCP代理进程:"
    for pid_file in /www/wwwroot/ai.guiyunai.fun/mcp-proxies/official/pids/*.pid; do
        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "  ✅ $name: ${GREEN}运行中${NC} (PID: $pid)"
            else
                echo -e "  ❌ $name: ${RED}已停止${NC}"
            fi
        fi
    done
else
    echo "  ⚠️ 官方MCP代理PID目录不存在"
fi

# 检查营销类MCP代理进程
if [ -d "/www/wwwroot/ai.guiyunai.fun/mcp-proxies/marketing/pids" ]; then
    echo "营销类MCP代理进程:"
    for pid_file in /www/wwwroot/ai.guiyunai.fun/mcp-proxies/marketing/pids/*.pid; do
        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "  ✅ $name: ${GREEN}运行中${NC} (PID: $pid)"
            else
                echo -e "  ❌ $name: ${RED}已停止${NC}"
            fi
        fi
    done
else
    echo "  ⚠️ 营销类MCP代理PID目录不存在"
fi

# 检查创作类MCP代理进程
if [ -d "/www/wwwroot/ai.guiyunai.fun/mcp-proxies/creative/pids" ]; then
    echo "创作类MCP代理进程:"
    for pid_file in /www/wwwroot/ai.guiyunai.fun/mcp-proxies/creative/pids/*.pid; do
        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "  ✅ $name: ${GREEN}运行中${NC} (PID: $pid)"
            else
                echo -e "  ❌ $name: ${RED}已停止${NC}"
            fi
        fi
    done
else
    echo "  ⚠️ 创作类MCP代理PID目录不存在"
fi

# 检查职业发展类MCP代理进程
if [ -d "/www/wwwroot/ai.guiyunai.fun/mcp-proxies/career/pids" ]; then
    echo "职业发展类MCP代理进程:"
    for pid_file in /www/wwwroot/ai.guiyunai.fun/mcp-proxies/career/pids/*.pid; do
        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "  ✅ $name: ${GREEN}运行中${NC} (PID: $pid)"
            else
                echo -e "  ❌ $name: ${RED}已停止${NC}"
            fi
        fi
    done
else
    echo "  ⚠️ 职业发展类MCP代理PID目录不存在"
fi

# 检查配置文件
echo -e "\n${YELLOW}📋 配置文件状态:${NC}"

config_file="/www/wwwroot/ai.guiyunai.fun/OpenHands/config.toml"
if [ -f "$config_file" ]; then
    echo -e "✅ config.toml: ${GREEN}存在${NC}"
    
    # 检查MCP配置
    if grep -q "sse_servers" "$config_file"; then
        echo -e "✅ MCP SSE配置: ${GREEN}已配置${NC}"
        
        # 统计配置的服务器数量
        server_count=$(grep -c "http://localhost:" "$config_file")
        echo -e "📊 配置的MCP服务器数量: ${BLUE}$server_count${NC}"
    else
        echo -e "❌ MCP SSE配置: ${RED}未配置${NC}"
    fi
else
    echo -e "❌ config.toml: ${RED}不存在${NC}"
fi

# 总结
echo -e "\n${BLUE}📊 状态总结:${NC}"
echo "=================================="

running_count=0
total_count=${#ports[@]}

for port in "${ports[@]}"; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        ((running_count++))
    fi
done

echo "MCP服务器运行状态: $running_count/$total_count"

if [ $running_count -eq $total_count ]; then
    echo -e "🎉 ${GREEN}所有MCP服务器运行正常！${NC}"
elif [ $running_count -gt 0 ]; then
    echo -e "⚠️ ${YELLOW}部分MCP服务器运行正常${NC}"
else
    echo -e "❌ ${RED}没有MCP服务器在运行${NC}"
fi

echo ""
echo "💡 管理命令:"
echo "  启动所有MCP服务器: cd /www/wwwroot/ai.guiyunai.fun && ./mcp-proxies/official/start_official_mcp_proxies.sh start"
echo "  查看详细状态: cd /www/wwwroot/ai.guiyunai.fun && ./mcp-proxies/official/start_official_mcp_proxies.sh status"

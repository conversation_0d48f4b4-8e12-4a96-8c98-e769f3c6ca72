#!/bin/bash

echo "🔧 修复OpenHands 502错误"
echo "================================"

echo "📊 当前状态检查..."
echo "Docker容器状态:"
docker ps | grep openhands-app

echo ""
echo "端口3000检查:"
curl -s -o /dev/null -w "HTTP状态: %{http_code}\n" http://localhost:3000 2>/dev/null || echo "连接失败"

echo ""
echo "🎯 问题分析:"
echo "容器在运行但服务无响应，可能原因："
echo "1. 容器内服务启动失败"
echo "2. 配置文件问题"
echo "3. 依赖缺失"

echo ""
echo "🔧 解决方案: 重新创建容器"

# 停止并删除现有容器
echo "停止现有容器..."
docker stop openhands-app 2>/dev/null || true
docker rm openhands-app 2>/dev/null || true

# 清理旧的运行时容器
echo "清理运行时容器..."
docker ps -q --filter "name=openhands-runtime" | xargs -r docker stop
docker ps -aq --filter "name=openhands-runtime" | xargs -r docker rm

echo ""
echo "🚀 重新启动OpenHands容器..."

# 确保配置文件存在
if [ ! -f "OpenHands/config.toml" ]; then
    echo "❌ 配置文件不存在，创建默认配置..."
    cp OpenHands/config.template.toml OpenHands/config.toml
fi

# 确保workspace目录存在
mkdir -p workspace

# 重新启动容器，使用更详细的配置
docker run -d \
    --name openhands-app \
    --restart unless-stopped \
    -p 3000:3000 \
    -v /www/wwwroot/ai.guiyunai.fun/OpenHands/config.toml:/app/config.toml:ro \
    -v /www/wwwroot/ai.guiyunai.fun/workspace:/workspace \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -e SANDBOX_USER_ID=1000 \
    -e LLM_API_KEY="********************************************************" \
    -e LLM_MODEL="groq/llama-3.3-70b-versatile" \
    -e LLM_BASE_URL="https://api.groq.com/openai/v1" \
    docker.all-hands.dev/all-hands-ai/openhands:0.51

echo ""
echo "⏳ 等待服务启动..."
sleep 20

echo ""
echo "🔍 检查服务状态..."
for i in {1..6}; do
    echo "尝试 $i/6..."
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200"; then
        echo "✅ 服务启动成功！"
        echo "🌐 本地访问: http://localhost:3000"
        echo "🌐 外部访问: https://ai.guiyunai.fun"
        
        # 检查外部访问
        echo ""
        echo "检查外部访问状态..."
        curl -s -o /dev/null -w "外部访问状态: %{http_code}\n" https://ai.guiyunai.fun
        
        exit 0
    fi
    sleep 10
done

echo ""
echo "❌ 服务启动失败，查看容器日志..."
docker logs openhands-app --tail 20

echo ""
echo "🔧 备用方案: 使用独立服务"
echo "如果容器方式持续失败，可以使用以下独立服务："

# 创建独立后端服务
cat << 'EOF' > start_backend_port8000.sh
#!/bin/bash
echo "🔧 启动独立后端服务 (端口8000)..."

export SANDBOX_USER_ID=1000
export LLM_API_KEY="********************************************************"
export LLM_MODEL="groq/llama-3.3-70b-versatile"
export LLM_BASE_URL="https://api.groq.com/openai/v1"
export RUNTIME="docker"
export WORKSPACE_BASE="/www/wwwroot/ai.guiyunai.fun/workspace"

cd /www/wwwroot/ai.guiyunai.fun/OpenHands

# 尝试使用Poetry环境
if [ -f "/root/.cache/pypoetry/virtualenvs/openhands-ai-qoVAMGzs-py3.12/bin/python" ]; then
    echo "使用Poetry虚拟环境启动后端..."
    /root/.cache/pypoetry/virtualenvs/openhands-ai-qoVAMGzs-py3.12/bin/python -m uvicorn openhands.server.listen:app --host 0.0.0.0 --port 8000
else
    echo "Poetry环境不存在，尝试系统Python..."
    cd /www/wwwroot/ai.guiyunai.fun/OpenHands
    python3 -m pip install -e .
    python3 -m uvicorn openhands.server.listen:app --host 0.0.0.0 --port 8000
fi
EOF

chmod +x start_backend_port8000.sh

echo ""
echo "📋 故障排除选项:"
echo "1. 等待更长时间: 容器可能需要更多时间启动"
echo "2. 查看日志: docker logs openhands-app -f"
echo "3. 使用独立后端: ./start_backend_port8000.sh"
echo "4. 检查系统资源: free -h && df -h"

echo ""
echo "🎯 推荐操作:"
echo "1. 先等待2-3分钟，然后访问 http://localhost:3000"
echo "2. 如果仍然失败，查看容器日志找出具体错误"
echo "3. 考虑使用独立服务模式进行调试"

#!/bin/bash

echo "🚀 OpenHands启动性能优化"
echo "================================"

# 1. 预拉取运行时镜像
echo "📦 预拉取运行时镜像..."
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik

# 2. 清理旧的运行时容器
echo "🧹 清理旧的运行时容器..."
docker ps -a --format "table {{.Names}}" | grep openhands-runtime | xargs -r docker rm -f

# 3. 优化Docker配置
echo "⚙️ 检查Docker配置..."
echo "当前Docker信息:"
docker info | grep -E "(Storage Driver|Logging Driver|Cgroup Driver)"

# 4. 预创建运行时容器（保持运行）
echo "🔧 创建预热运行时容器..."
docker run -d \
  --name openhands-runtime-prewarmed \
  --restart unless-stopped \
  -v /www/wwwroot/ai.guiyunai.fun/workspace:/workspace \
  docker.all-hands.dev/all-hands-ai/runtime:0.51-ni<PERSON><PERSON>k \
  sleep infinity

# 5. 优化系统资源
echo "💾 检查系统资源..."
echo "内存使用情况:"
free -h

echo ""
echo "磁盘使用情况:"
df -h | grep -E "(/$|/var|/tmp)"

echo ""
echo "Docker磁盘使用:"
docker system df

# 6. 网络优化
echo "🌐 网络优化..."
echo "检查DNS解析速度:"
time nslookup docker.all-hands.dev >/dev/null 2>&1 || echo "DNS解析可能较慢"

# 7. 生成优化配置
cat << 'EOF' > docker-compose.override.yml
# OpenHands性能优化配置
version: '3.8'

services:
  openhands-app:
    environment:
      # 运行时优化
      - SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik
      - SANDBOX_KEEP_RUNTIME_ALIVE=true
      - SANDBOX_PAUSE_CLOSED_RUNTIMES=false
      - SANDBOX_REMOTE_RUNTIME_INIT_TIMEOUT=30
      # 性能优化
      - SANDBOX_TIMEOUT=300
      - MAX_ITERATIONS=50
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # 预热运行时服务
  runtime-prewarmer:
    image: docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik
    command: sleep infinity
    volumes:
      - ./workspace:/workspace
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
EOF

echo "✅ 优化配置已生成: docker-compose.override.yml"

echo ""
echo "🎯 优化建议："
echo "1. 【立即生效】重启OpenHands服务"
echo "2. 【系统级】增加系统内存（推荐8GB+）"
echo "3. 【存储优化】使用SSD存储"
echo "4. 【网络优化】配置Docker镜像加速器"

echo ""
echo "📊 性能监控命令："
echo "docker stats --no-stream  # 查看容器资源使用"
echo "docker logs openhands-app --tail 50  # 查看启动日志"
echo "time docker run --rm hello-world  # 测试Docker启动速度"

# OpenHands 项目 .gitignore

# 敏感信息 - 不要提交到Git
**/*api_key*
**/*secret*
**/*token*
**/*password*
.env
.env.local
.env.production

# OpenHands 运行时文件
workspace/
logs/
*.log
.openhands/sessions/
.openhands/.jwt_secret

# Docker 相关
docker-compose.override.yml

# 系统文件
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# 临时文件
tmp/
temp/
*.tmp
*.bak
*.backup

# 大文件和二进制文件
*.zip
*.tar.gz
*.rar
*.7z
*.dmg
*.iso

# 调试文件
debug-*.js
playwright-report/
test-results/

# 备份文件
*.backup.*
backup_*
*_backup

#!/usr/bin/env python3
"""
集成测试 - 验证MCP服务器与OpenHands的集成
"""

import pytest
import asyncio
import json
import tempfile
import os
import sys
from unittest.mock import patch, MagicMock, AsyncMock

# 添加路径
sys.path.append('../mcp_servers')
sys.path.append('../OpenHands/mcp_servers')

class TestMCPIntegration:
    """测试MCP服务器与OpenHands的集成"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.yijing_db_path = os.path.join(self.temp_dir, "yijing.db")
        self.fengshui_db_path = os.path.join(self.temp_dir, "fengshui.db")
        
        # 设置环境变量
        os.environ["YIJING_DB_PATH"] = self.yijing_db_path
        os.environ["FENGSHUI_RULES_PATH"] = self.fengshui_db_path
        os.environ["UPLOAD_PATH"] = os.path.join(self.temp_dir, "uploads")
    
    def teardown_method(self):
        """清理测试环境"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_mcp_config_loading(self):
        """测试MCP配置加载"""
        # 模拟OpenHands的MCP配置
        mcp_config = {
            "stdio_servers": [
                {
                    "name": "yijing-knowledge",
                    "command": "python",
                    "args": ["-m", "mcp_servers.yijing_server"],
                    "env": {
                        "YIJING_DB_PATH": self.yijing_db_path,
                        "LANGUAGE": "zh-CN"
                    }
                },
                {
                    "name": "fengshui-analysis",
                    "command": "python", 
                    "args": ["-m", "mcp_servers.fengshui_server"],
                    "env": {
                        "FENGSHUI_RULES_PATH": self.fengshui_db_path,
                        "LANGUAGE": "zh-CN"
                    }
                }
            ]
        }
        
        # 验证配置结构
        assert "stdio_servers" in mcp_config
        assert len(mcp_config["stdio_servers"]) == 2
        
        # 验证易经服务器配置
        yijing_config = mcp_config["stdio_servers"][0]
        assert yijing_config["name"] == "yijing-knowledge"
        assert "YIJING_DB_PATH" in yijing_config["env"]
        
        # 验证风水服务器配置
        fengshui_config = mcp_config["stdio_servers"][1]
        assert fengshui_config["name"] == "fengshui-analysis"
        assert "FENGSHUI_RULES_PATH" in fengshui_config["env"]
    
    @pytest.mark.asyncio
    async def test_mcp_client_connection(self):
        """测试MCP客户端连接"""
        # 模拟MCP客户端连接过程
        mock_client = AsyncMock()
        mock_client.list_tools.return_value = [
            {
                "name": "yijing_hexagram_query",
                "description": "查询易经六十四卦",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string"},
                        "method": {"type": "string"}
                    }
                }
            }
        ]
        
        # 模拟工具调用
        mock_client.call_tool.return_value = {
            "content": [{"type": "text", "text": json.dumps({"卦名": "乾卦", "状态": "成功"})}]
        }
        
        # 验证连接和工具调用
        tools = await mock_client.list_tools()
        assert len(tools) == 1
        assert tools[0]["name"] == "yijing_hexagram_query"
        
        result = await mock_client.call_tool("yijing_hexagram_query", {"query": "乾", "method": "name"})
        assert "content" in result
    
    def test_mcp_action_creation(self):
        """测试MCP动作创建"""
        # 模拟OpenHands的MCPAction
        class MockMCPAction:
            def __init__(self, name: str, arguments: dict):
                self.name = name
                self.arguments = arguments
        
        # 创建易经查询动作
        yijing_action = MockMCPAction(
            name="yijing_hexagram_query",
            arguments={"query": "乾卦", "method": "name"}
        )
        
        assert yijing_action.name == "yijing_hexagram_query"
        assert yijing_action.arguments["query"] == "乾卦"
        
        # 创建风水分析动作
        fengshui_action = MockMCPAction(
            name="fengshui_house_analysis",
            arguments={
                "house_direction": "正南",
                "door_direction": "正北",
                "layout_description": "三室两厅",
                "birth_year": 1990
            }
        )
        
        assert fengshui_action.name == "fengshui_house_analysis"
        assert fengshui_action.arguments["house_direction"] == "正南"
    
    def test_mcp_observation_parsing(self):
        """测试MCP观察结果解析"""
        # 模拟MCP服务器返回的结果
        yijing_result = {
            "卦号": 1,
            "卦名": "乾",
            "卦辞": "元亨利贞",
            "解释": "象征创造力和领导力"
        }
        
        fengshui_result = {
            "基本信息": {
                "房屋朝向": "正南",
                "大门朝向": "正北"
            },
            "八宅分析": {
                "吉凶": "中吉",
                "说明": "整体运势平稳"
            }
        }
        
        # 验证结果结构
        assert "卦名" in yijing_result
        assert yijing_result["卦名"] == "乾"
        
        assert "基本信息" in fengshui_result
        assert fengshui_result["基本信息"]["房屋朝向"] == "正南"
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """测试错误处理"""
        # 模拟连接失败
        mock_client = AsyncMock()
        mock_client.list_tools.side_effect = Exception("连接失败")
        
        try:
            await mock_client.list_tools()
            assert False, "应该抛出异常"
        except Exception as e:
            assert "连接失败" in str(e)
        
        # 模拟工具调用失败
        mock_client.call_tool.side_effect = Exception("工具调用失败")
        
        try:
            await mock_client.call_tool("invalid_tool", {})
            assert False, "应该抛出异常"
        except Exception as e:
            assert "工具调用失败" in str(e)
    
    def test_data_validation(self):
        """测试数据验证"""
        # 验证易经查询参数
        valid_yijing_params = {
            "query": "乾卦",
            "method": "name"
        }
        
        assert isinstance(valid_yijing_params["query"], str)
        assert valid_yijing_params["method"] in ["name", "number", "situation"]
        
        # 验证风水分析参数
        valid_fengshui_params = {
            "house_direction": "正南",
            "door_direction": "正北",
            "layout_description": "三室两厅",
            "birth_year": 1990
        }
        
        assert isinstance(valid_fengshui_params["house_direction"], str)
        assert isinstance(valid_fengshui_params["birth_year"], int)
        assert 1900 <= valid_fengshui_params["birth_year"] <= 2100
    
    def test_chinese_encoding(self):
        """测试中文编码处理"""
        # 测试中文字符串处理
        chinese_text = "易经八卦风水堪舆"
        encoded = chinese_text.encode('utf-8')
        decoded = encoded.decode('utf-8')
        
        assert decoded == chinese_text
        
        # 测试JSON序列化
        chinese_data = {
            "卦名": "乾卦",
            "解释": "天行健，君子以自强不息"
        }
        
        json_str = json.dumps(chinese_data, ensure_ascii=False)
        parsed_data = json.loads(json_str)
        
        assert parsed_data["卦名"] == "乾卦"
        assert "自强不息" in parsed_data["解释"]


class TestWorkflowIntegration:
    """测试工作流集成"""
    
    def test_agent_mcp_workflow(self):
        """测试Agent与MCP的工作流"""
        # 模拟Agent接收用户请求
        user_request = "请帮我分析一下乾卦的含义"
        
        # 模拟Agent解析请求并选择MCP工具
        selected_tool = "yijing_hexagram_query"
        tool_params = {"query": "乾", "method": "name"}
        
        # 验证工具选择
        assert selected_tool == "yijing_hexagram_query"
        assert tool_params["query"] == "乾"
        
        # 模拟MCP工具执行结果
        mcp_result = {
            "卦名": "乾",
            "卦辞": "元亨利贞",
            "解释": "象征创造力、领导力、积极进取的精神"
        }
        
        # 模拟Agent处理结果并生成回复
        agent_response = f"根据易经分析，{mcp_result['卦名']}卦的含义是：{mcp_result['解释']}"
        
        assert "乾卦" in agent_response
        assert "创造力" in agent_response
    
    def test_multi_tool_workflow(self):
        """测试多工具协作工作流"""
        # 模拟复杂请求：既需要易经分析又需要风水建议
        user_request = "我想了解乾卦的含义，并且分析一下正南朝向的房子风水如何"
        
        # 第一步：易经分析
        yijing_tool = "yijing_hexagram_query"
        yijing_params = {"query": "乾", "method": "name"}
        yijing_result = {"卦名": "乾", "解释": "象征创造力和领导力"}
        
        # 第二步：风水分析
        fengshui_tool = "fengshui_house_analysis"
        fengshui_params = {
            "house_direction": "正南",
            "door_direction": "正北",
            "layout_description": "标准户型"
        }
        fengshui_result = {"基本信息": {"房屋朝向": "正南"}, "八宅分析": {"吉凶": "中吉"}}
        
        # 验证多工具协作
        assert yijing_tool != fengshui_tool
        assert yijing_result["卦名"] == "乾"
        assert fengshui_result["八宅分析"]["吉凶"] == "中吉"
    
    def test_error_recovery_workflow(self):
        """测试错误恢复工作流"""
        # 模拟工具调用失败
        error_scenarios = [
            {"tool": "yijing_hexagram_query", "error": "数据库连接失败"},
            {"tool": "fengshui_house_analysis", "error": "参数验证失败"},
        ]
        
        for scenario in error_scenarios:
            # 模拟错误处理
            fallback_response = f"抱歉，{scenario['tool']}服务暂时不可用：{scenario['error']}"
            
            assert "抱歉" in fallback_response
            assert scenario['tool'] in fallback_response


class TestPerformanceIntegration:
    """测试性能集成"""
    
    def test_response_time_requirements(self):
        """测试响应时间要求"""
        import time
        
        # 模拟快速查询
        start_time = time.time()
        
        # 模拟数据库查询（应该很快）
        mock_query_time = 0.1  # 100ms
        time.sleep(mock_query_time)
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # 验证响应时间在可接受范围内（< 1秒）
        assert response_time < 1.0, f"响应时间过长: {response_time}秒"
    
    def test_concurrent_requests(self):
        """测试并发请求处理"""
        import threading
        import time
        
        results = []
        
        def mock_request(request_id):
            # 模拟并发请求
            time.sleep(0.1)  # 模拟处理时间
            results.append(f"请求{request_id}完成")
        
        # 创建多个并发请求
        threads = []
        for i in range(5):
            thread = threading.Thread(target=mock_request, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有请求完成
        for thread in threads:
            thread.join()
        
        # 验证所有请求都完成了
        assert len(results) == 5
        assert all("完成" in result for result in results)


if __name__ == "__main__":
    # 运行集成测试
    print("🧪 开始集成测试")
    
    # 简单的测试运行器
    test_classes = [TestMCPIntegration, TestWorkflowIntegration, TestPerformanceIntegration]
    
    for test_class in test_classes:
        print(f"\n📋 运行 {test_class.__name__}")
        instance = test_class()
        
        # 运行setup
        if hasattr(instance, 'setup_method'):
            instance.setup_method()
        
        # 运行测试方法
        for method_name in dir(instance):
            if method_name.startswith('test_'):
                try:
                    method = getattr(instance, method_name)
                    if asyncio.iscoroutinefunction(method):
                        asyncio.run(method())
                    else:
                        method()
                    print(f"  ✅ {method_name}")
                except Exception as e:
                    print(f"  ❌ {method_name}: {e}")
        
        # 运行teardown
        if hasattr(instance, 'teardown_method'):
            instance.teardown_method()
    
    print("\n🎯 集成测试完成")

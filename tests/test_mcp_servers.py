#!/usr/bin/env python3
"""
MCP服务器单元测试
测试风水和易经MCP服务器的功能
"""

import pytest
import asyncio
import json
import sqlite3
import tempfile
import os
from unittest.mock import patch, MagicMock
from typing import Dict, Any

# 导入我们的MCP服务器
import sys
sys.path.append('../mcp_servers')
sys.path.append('../OpenHands/mcp_servers')

class TestMCPServerBasics:
    """测试MCP服务器基础功能"""
    
    def test_mcp_import(self):
        """测试MCP库导入"""
        try:
            from mcp.server import Server
            from mcp.types import Tool
            assert True, "MCP库导入成功"
        except ImportError as e:
            pytest.fail(f"MCP库导入失败: {e}")
    
    def test_server_creation(self):
        """测试MCP服务器创建"""
        try:
            from mcp.server import Server
            server = Server("test-server")
            assert server.name == "test-server"
        except Exception as e:
            pytest.fail(f"MCP服务器创建失败: {e}")
    
    def test_tool_registration(self):
        """测试工具注册"""
        try:
            from mcp.server import Server
            server = Server("test-server")
            
            @server.call_tool()
            async def test_tool(query: str) -> str:
                """测试工具"""
                return f"收到查询: {query}"
            
            assert True, "工具注册成功"
        except Exception as e:
            pytest.fail(f"工具注册失败: {e}")


class TestSimpleYijingServer:
    """测试简化版易经服务器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test_yijing.db")
        
        # 设置环境变量
        os.environ["YIJING_DB_PATH"] = self.db_path
        os.environ["UPLOAD_PATH"] = os.path.join(self.temp_dir, "uploads")
    
    def teardown_method(self):
        """清理测试环境"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_server_initialization(self):
        """测试服务器初始化"""
        try:
            from OpenHands.mcp_servers.simple_yijing_server import SimpleYijingServer
            server = SimpleYijingServer()
            assert os.path.exists(self.db_path), "数据库文件应该被创建"
        except Exception as e:
            pytest.fail(f"服务器初始化失败: {e}")
    
    def test_database_creation(self):
        """测试数据库创建"""
        from OpenHands.mcp_servers.simple_yijing_server import SimpleYijingServer
        server = SimpleYijingServer()
        
        # 检查数据库表是否存在
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        assert "hexagrams" in tables, "hexagrams表应该存在"
        assert "uploaded_documents" in tables, "uploaded_documents表应该存在"
        
        conn.close()
    
    def test_hexagram_query(self):
        """测试卦象查询"""
        from OpenHands.mcp_servers.simple_yijing_server import SimpleYijingServer
        server = SimpleYijingServer()
        
        # 测试按名称查询
        result = server.query_hexagram("乾", "name")
        assert result["状态"] == "成功", "查询应该成功"
        assert result["卦名"] == "乾", "应该返回乾卦"
        
        # 测试按编号查询
        result = server.query_hexagram("1", "number")
        assert result["状态"] == "成功", "按编号查询应该成功"
        assert result["卦号"] == 1, "应该返回第1卦"
    
    def test_upload_knowledge(self):
        """测试知识上传"""
        from OpenHands.mcp_servers.simple_yijing_server import SimpleYijingServer
        server = SimpleYijingServer()
        
        test_content = "这是一个测试文档，包含易经相关知识。"
        result = server.upload_knowledge("test.txt", test_content, "test", "测试文档")
        
        assert result["状态"] == "成功", "上传应该成功"
        assert "文档ID" in result, "应该返回文档ID"
    
    def test_search_knowledge(self):
        """测试知识搜索"""
        from OpenHands.mcp_servers.simple_yijing_server import SimpleYijingServer
        server = SimpleYijingServer()
        
        # 先上传一些测试数据
        server.upload_knowledge("test1.txt", "乾卦相关内容", "hexagram", "乾卦说明")
        
        # 搜索
        result = server.search_knowledge("乾卦")
        assert "查询" in result, "应该返回查询结果"
        assert result["总数"] > 0, "应该找到相关内容"
    
    def test_divination_analysis(self):
        """测试占卜分析"""
        from OpenHands.mcp_servers.simple_yijing_server import SimpleYijingServer
        server = SimpleYijingServer()
        
        result = server.divination_analysis("我的事业发展如何？", 1)
        assert "问题" in result, "应该包含问题"
        assert "主卦" in result, "应该包含主卦信息"
        assert "建议" in result, "应该包含建议"
    
    def test_statistics(self):
        """测试统计信息"""
        from OpenHands.mcp_servers.simple_yijing_server import SimpleYijingServer
        server = SimpleYijingServer()
        
        stats = server.get_statistics()
        assert "卦象数量" in stats, "应该包含卦象数量"
        assert "上传文档" in stats, "应该包含上传文档数量"
        assert stats["卦象数量"] >= 8, "应该至少有8个基础卦象"


class TestErrorHandling:
    """测试错误处理"""
    
    def test_invalid_hexagram_query(self):
        """测试无效卦象查询"""
        from OpenHands.mcp_servers.simple_yijing_server import SimpleYijingServer
        
        with tempfile.TemporaryDirectory() as temp_dir:
            os.environ["YIJING_DB_PATH"] = os.path.join(temp_dir, "test.db")
            server = SimpleYijingServer()
            
            result = server.query_hexagram("不存在的卦", "name")
            assert result["状态"] == "未找到", "应该返回未找到状态"
    
    def test_invalid_hexagram_number(self):
        """测试无效卦象编号"""
        from OpenHands.mcp_servers.simple_yijing_server import SimpleYijingServer
        
        with tempfile.TemporaryDirectory() as temp_dir:
            os.environ["YIJING_DB_PATH"] = os.path.join(temp_dir, "test.db")
            server = SimpleYijingServer()
            
            result = server.query_hexagram("999", "number")
            assert result["状态"] == "未找到", "应该返回未找到状态"


class TestDatabaseOperations:
    """测试数据库操作"""
    
    def test_database_integrity(self):
        """测试数据库完整性"""
        from OpenHands.mcp_servers.simple_yijing_server import SimpleYijingServer
        
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, "test.db")
            os.environ["YIJING_DB_PATH"] = db_path
            
            server = SimpleYijingServer()
            
            # 检查数据库文件存在
            assert os.path.exists(db_path), "数据库文件应该存在"
            
            # 检查基础数据
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM hexagrams")
            count = cursor.fetchone()[0]
            assert count >= 8, "应该有基础的卦象数据"
            conn.close()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])

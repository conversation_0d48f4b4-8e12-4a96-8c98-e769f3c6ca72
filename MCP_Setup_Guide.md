# 全面助理MCP服务器配置指南

## 🎯 系统概述

我们已经为您创建了一个全面的助理系统，包含6个专业的MCP服务器，覆盖设计、编程、营销、剧本杀和传统文化等领域。

### 🏗️ 服务器架构

```
全能助理MCP生态系统
├── 设计创意服务器 (design-creative)
│   ├── Logo概念设计
│   ├── UI界面布局设计  
│   ├── 品牌识别系统
│   ├── 色彩搭配分析
│   └── 字体推荐系统
├── 编程开发服务器 (programming-dev)
│   ├── 代码结构生成
│   ├── 性能优化分析
│   ├── API规范设计
│   ├── 开发环境配置
│   └── 测试套件生成
├── 营销推广服务器 (marketing-promotion)
│   ├── 社交媒体策略
│   ├── 内容日历生成
│   ├── 营销文案创作
│   ├── 竞品策略分析
│   └── 活动优化建议
├── 剧本杀服务器 (script-murder)
│   ├── 剧本创作工具
│   ├── 角色档案设计
│   ├── 线索系统生成
│   ├── 游戏机制设计
│   └── 主持人指南
├── 风水分析服务器 (fengshui-analysis)
│   ├── 住宅风水分析
│   ├── 方位吉凶分析
│   ├── 五行平衡计算
│   └── 房间布局建议
└── 易经占卜服务器 (yijing-knowledge)
    ├── 六十四卦查询
    ├── 占卜分析工具
    ├── 哲学人生指导
    └── 变爻分析系统
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装MCP Python SDK
pip install mcp

# 安装SuperGateway（推荐，用于代理模式）
npm install -g @supercorp-ai/supergateway
# 或者
pip install supergateway
```

### 2. 启动服务器

#### 方式一：使用管理脚本（推荐）

```bash
# 检查依赖
python start_mcp_servers.py check

# 启动所有服务器（代理模式）
python start_mcp_servers.py start-all

# 启动单个服务器
python start_mcp_servers.py start --server design

# 查看可用服务器
python start_mcp_servers.py list
```

#### 方式二：手动启动代理服务器

```bash
# 终端1：设计创意服务器
supergateway --stdio "python -m mcp_servers.design_creative_server" --port 8080

# 终端2：编程开发服务器  
supergateway --stdio "python -m mcp_servers.programming_dev_server" --port 8081

# 终端3：营销推广服务器
supergateway --stdio "python -m mcp_servers.marketing_promotion_server" --port 8082

# 终端4：剧本杀服务器
supergateway --stdio "python -m mcp_servers.script_murder_server" --port 8083

# 终端5：风水分析服务器
supergateway --stdio "python -m mcp_servers.fengshui_server" --port 8084

# 终端6：易经占卜服务器
supergateway --stdio "python -m mcp_servers.yijing_server" --port 8085
```

### 3. 配置OpenHands

#### 方式一：通过UI配置（推荐）

1. 打开OpenHands界面
2. 进入Settings → MCP标签
3. 添加SSE服务器地址：
   ```
   http://localhost:8080/sse  # 设计创意服务器
   http://localhost:8081/sse  # 编程开发服务器
   http://localhost:8082/sse  # 营销推广服务器
   http://localhost:8083/sse  # 剧本杀服务器
   http://localhost:8084/sse  # 风水分析服务器
   http://localhost:8085/sse  # 易经占卜服务器
   ```

#### 方式二：修改config.toml

配置文件已经为您准备好了，位于 `OpenHands/config.toml`：

```toml
[mcp]
# SSE服务器配置（推荐）
sse_servers = [
    "http://localhost:8080/sse",  # 设计创意
    "http://localhost:8081/sse",  # 编程开发
    "http://localhost:8082/sse",  # 营销推广
    "http://localhost:8083/sse",  # 剧本杀
    "http://localhost:8084/sse",  # 风水分析
    "http://localhost:8085/sse"   # 易经占卜
]

# 或者使用stdio服务器（开发测试用）
stdio_servers = [
    {
        name = "design-creative"
        command = "python"
        args = ["-m", "mcp_servers.design_creative_server"]
        env = { LANGUAGE = "zh-CN" }
    },
    # ... 其他服务器配置
]
```

## 🛠️ 服务器功能详解

### 设计创意服务器
- **generate_logo_concept**: Logo概念设计
- **design_ui_layout**: UI界面布局设计
- **create_brand_identity**: 品牌识别系统创建
- **analyze_color_palette**: 色彩搭配分析
- **recommend_typography**: 字体推荐

### 编程开发服务器
- **generate_code_structure**: 代码结构生成
- **optimize_code_performance**: 代码性能优化
- **design_api_specification**: API规范设计
- **setup_development_environment**: 开发环境配置
- **generate_test_suite**: 测试套件生成

### 营销推广服务器
- **create_social_media_strategy**: 社交媒体策略制定
- **generate_content_calendar**: 内容日历生成
- **write_marketing_copy**: 营销文案创作
- **analyze_competitor_strategy**: 竞品策略分析
- **optimize_campaign_performance**: 活动优化

### 剧本杀服务器
- **create_murder_script**: 剧本创作
- **design_character_profiles**: 角色档案设计
- **generate_clue_system**: 线索系统生成
- **create_game_mechanics**: 游戏机制设计
- **generate_host_guide**: 主持人指南

### 传统文化服务器
- **fengshui_house_analysis**: 住宅风水分析
- **yijing_hexagram_query**: 易经卦象查询
- **yijing_divination_analysis**: 占卜分析
- **fengshui_wuxing_balance**: 五行平衡分析

## 🔧 故障排除

### 常见问题

1. **服务器启动失败**
   ```bash
   # 检查端口是否被占用
   netstat -an | grep :8080
   
   # 检查Python模块是否正确安装
   python -c "import mcp.server.fastmcp; print('MCP安装正确')"
   ```

2. **OpenHands无法连接**
   - 确保服务器正在运行
   - 检查防火墙设置
   - 验证配置文件格式

3. **工具无法调用**
   - 检查服务器日志
   - 验证工具参数格式
   - 确认服务器注册成功

### 日志查看

```bash
# 查看服务器日志
python -m mcp_servers.design_creative_server 2>&1 | tee design_server.log
```

## 📊 性能优化

### 推荐配置

1. **使用SSE代理模式**：更稳定，支持负载均衡
2. **合理分配端口**：避免端口冲突
3. **监控资源使用**：定期检查内存和CPU使用情况

### 扩展建议

1. **添加更多服务器**：根据需要扩展特定领域
2. **集群部署**：使用Docker容器化部署
3. **负载均衡**：使用Nginx进行负载均衡

## 🎉 开始使用

现在您可以在OpenHands中使用这些强大的工具了！

**示例对话：**
- "帮我设计一个科技公司的Logo"
- "为我的React项目生成代码结构"
- "制定一个微信营销策略"
- "创作一个古风剧本杀"
- "分析我家的风水布局"
- "用易经占卜分析事业发展"

享受您的全能AI助理吧！🚀

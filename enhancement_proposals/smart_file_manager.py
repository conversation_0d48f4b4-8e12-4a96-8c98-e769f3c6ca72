"""
OpenHands智能文件管理增强方案
基于现有FileReadAction和FileWriteAction扩展，实现智能文件操作
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Union
import os
import json
import mimetypes
from pathlib import Path
import ast

@dataclass
class FileContext:
    """文件上下文"""
    path: str
    file_type: str
    size: int
    encoding: str
    language: Optional[str] = None
    structure: Optional[Dict] = None
    dependencies: List[str] = None
    
@dataclass
class SmartViewResult:
    """智能查看结果"""
    content: str
    context: FileContext
    highlights: List[Dict[str, Any]]
    suggestions: List[str]
    related_files: List[str]

class SmartFileManager:
    """智能文件管理器"""
    
    def __init__(self, workspace_path: str):
        self.workspace_path = Path(workspace_path)
        self.file_cache = {}
        self.project_structure = {}
        
    async def intelligent_view(self, 
                             file_path: str,
                             focus: Optional[str] = None,
                             context_lines: int = 5) -> SmartViewResult:
        """
        智能文件查看
        根据文件类型和查询焦点提供智能化的文件内容展示
        
        Args:
            file_path: 文件路径
            focus: 关注点 (function_name, class_name, keyword等)
            context_lines: 上下文行数
            
        Returns:
            智能查看结果
        """
        # 1. 分析文件上下文
        file_context = await self._analyze_file_context(file_path)
        
        # 2. 读取文件内容
        content = await self._read_file_with_encoding(file_path, file_context.encoding)
        
        # 3. 根据焦点过滤内容
        if focus:
            filtered_content, highlights = await self._filter_content_by_focus(
                content, focus, file_context, context_lines
            )
        else:
            filtered_content = content
            highlights = await self._auto_highlight_important_parts(content, file_context)
        
        # 4. 生成建议
        suggestions = await self._generate_view_suggestions(file_context, focus)
        
        # 5. 查找相关文件
        related_files = await self._find_related_files(file_path, file_context)
        
        return SmartViewResult(
            content=filtered_content,
            context=file_context,
            highlights=highlights,
            suggestions=suggestions,
            related_files=related_files
        )
    
    async def intelligent_save(self, 
                             file_path: str,
                             content: str,
                             save_mode: str = "smart") -> Dict[str, Any]:
        """
        智能文件保存
        根据文件类型和内容自动优化保存过程
        
        Args:
            file_path: 文件路径
            content: 文件内容
            save_mode: 保存模式 (smart, force, backup)
            
        Returns:
            保存结果信息
        """
        # 1. 预保存分析
        analysis = await self._pre_save_analysis(file_path, content)
        
        # 2. 内容验证和优化
        if analysis["file_type"] in ["python", "javascript", "typescript"]:
            content = await self._optimize_code_content(content, analysis["file_type"])
            validation = await self._validate_code_syntax(content, analysis["file_type"])
            if not validation["valid"] and save_mode != "force":
                return {
                    "success": False,
                    "error": "Syntax validation failed",
                    "details": validation["errors"]
                }
        
        # 3. 备份处理
        if save_mode in ["smart", "backup"] and os.path.exists(file_path):
            backup_path = await self._create_backup(file_path)
        else:
            backup_path = None
        
        # 4. 保存文件
        try:
            await self._save_file_with_encoding(file_path, content, analysis["encoding"])
            
            # 5. 后保存处理
            post_save_info = await self._post_save_processing(file_path, analysis)
            
            return {
                "success": True,
                "file_path": file_path,
                "backup_path": backup_path,
                "file_size": len(content.encode(analysis["encoding"])),
                "encoding": analysis["encoding"],
                "post_processing": post_save_info
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "backup_path": backup_path
            }
    
    async def smart_file_navigation(self, 
                                  query: str,
                                  file_types: List[str] = None) -> List[Dict[str, Any]]:
        """
        智能文件导航
        基于查询智能推荐相关文件
        """
        # 1. 构建项目文件索引
        if not self.project_structure:
            await self._build_project_index()
        
        # 2. 解析查询意图
        intent = self._parse_navigation_intent(query)
        
        # 3. 搜索匹配文件
        matches = []
        
        # 按文件名搜索
        name_matches = await self._search_by_filename(intent["keywords"])
        matches.extend(name_matches)
        
        # 按内容搜索
        if intent["search_content"]:
            content_matches = await self._search_by_content(intent["keywords"])
            matches.extend(content_matches)
        
        # 按文件类型过滤
        if file_types:
            matches = [m for m in matches if m["file_type"] in file_types]
        
        # 4. 排序和排名
        ranked_matches = await self._rank_file_matches(matches, intent)
        
        return ranked_matches[:20]  # 返回前20个匹配
    
    async def project_structure_analysis(self) -> Dict[str, Any]:
        """
        项目结构分析
        分析整个项目的文件结构和依赖关系
        """
        await self._build_project_index()
        
        analysis = {
            "total_files": len(self.project_structure),
            "file_types": await self._analyze_file_types(),
            "directory_structure": await self._analyze_directory_structure(),
            "dependency_graph": await self._build_dependency_graph(),
            "code_metrics": await self._calculate_code_metrics(),
            "potential_issues": await self._identify_potential_issues()
        }
        
        return analysis
    
    async def _analyze_file_context(self, file_path: str) -> FileContext:
        """分析文件上下文"""
        path_obj = Path(file_path)
        
        # 基础信息
        file_size = path_obj.stat().st_size if path_obj.exists() else 0
        mime_type, _ = mimetypes.guess_type(file_path)
        
        # 检测编码
        encoding = await self._detect_file_encoding(file_path)
        
        # 检测编程语言
        language = self._detect_programming_language(file_path)
        
        # 分析文件结构
        structure = None
        if language in ["python", "javascript", "typescript"]:
            structure = await self._analyze_code_structure(file_path, language)
        
        # 分析依赖
        dependencies = await self._analyze_file_dependencies(file_path, language)
        
        return FileContext(
            path=file_path,
            file_type=language or mime_type or "text",
            size=file_size,
            encoding=encoding,
            language=language,
            structure=structure,
            dependencies=dependencies
        )
    
    async def _filter_content_by_focus(self, 
                                     content: str,
                                     focus: str,
                                     file_context: FileContext,
                                     context_lines: int) -> tuple[str, List[Dict]]:
        """根据焦点过滤内容"""
        lines = content.split('\n')
        filtered_lines = []
        highlights = []
        
        if file_context.language == "python":
            # Python特定的焦点过滤
            if focus.startswith("class:"):
                class_name = focus.split(":", 1)[1]
                filtered_lines, highlights = await self._extract_python_class(
                    lines, class_name, context_lines
                )
            elif focus.startswith("function:"):
                func_name = focus.split(":", 1)[1]
                filtered_lines, highlights = await self._extract_python_function(
                    lines, func_name, context_lines
                )
        
        # 通用关键词搜索
        if not filtered_lines:
            for i, line in enumerate(lines):
                if focus.lower() in line.lower():
                    start = max(0, i - context_lines)
                    end = min(len(lines), i + context_lines + 1)
                    filtered_lines.extend(lines[start:end])
                    highlights.append({
                        "line": i,
                        "type": "keyword_match",
                        "text": focus
                    })
        
        return '\n'.join(filtered_lines) if filtered_lines else content, highlights
    
    async def _extract_python_class(self, 
                                   lines: List[str],
                                   class_name: str,
                                   context_lines: int) -> tuple[List[str], List[Dict]]:
        """提取Python类定义"""
        class_lines = []
        highlights = []
        
        for i, line in enumerate(lines):
            if line.strip().startswith(f"class {class_name}"):
                # 找到类定义，提取整个类
                indent_level = len(line) - len(line.lstrip())
                class_start = i
                class_end = i + 1
                
                # 找到类的结束位置
                for j in range(i + 1, len(lines)):
                    if lines[j].strip() and len(lines[j]) - len(lines[j].lstrip()) <= indent_level:
                        if not lines[j].strip().startswith(('"""', "'''", "#")):
                            class_end = j
                            break
                else:
                    class_end = len(lines)
                
                # 添加上下文
                start = max(0, class_start - context_lines)
                end = min(len(lines), class_end + context_lines)
                
                class_lines = lines[start:end]
                highlights.append({
                    "line": class_start - start,
                    "type": "class_definition",
                    "text": class_name
                })
                break
        
        return class_lines, highlights
    
    def _detect_programming_language(self, file_path: str) -> Optional[str]:
        """检测编程语言"""
        extension_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin'
        }
        
        suffix = Path(file_path).suffix.lower()
        return extension_map.get(suffix)

# MCP工具配置
MCP_FILE_MANAGER_TOOLS = [
    {
        "name": "intelligent_view",
        "description": "智能文件查看，支持焦点过滤和上下文分析",
        "parameters": {
            "type": "object",
            "properties": {
                "file_path": {"type": "string"},
                "focus": {"type": "string", "description": "关注点，如 'class:MyClass' 或 'function:my_func'"},
                "context_lines": {"type": "integer", "default": 5}
            },
            "required": ["file_path"]
        }
    },
    {
        "name": "intelligent_save",
        "description": "智能文件保存，包含语法验证和自动优化",
        "parameters": {
            "type": "object",
            "properties": {
                "file_path": {"type": "string"},
                "content": {"type": "string"},
                "save_mode": {"type": "string", "enum": ["smart", "force", "backup"], "default": "smart"}
            },
            "required": ["file_path", "content"]
        }
    },
    {
        "name": "smart_file_navigation",
        "description": "智能文件导航和搜索",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "搜索查询"},
                "file_types": {"type": "array", "items": {"type": "string"}}
            },
            "required": ["query"]
        }
    },
    {
        "name": "project_structure_analysis",
        "description": "项目结构分析",
        "parameters": {
            "type": "object",
            "properties": {},
            "required": []
        }
    }
]

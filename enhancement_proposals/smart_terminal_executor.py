"""
OpenHands智能终端执行增强方案
基于现有CmdRunAction扩展，实现类似Augment的智能命令执行
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional, AsyncGenerator
import asyncio
import json
from pathlib import Path

@dataclass
class ExecutionContext:
    """执行上下文"""
    command: str
    working_directory: str
    environment: Dict[str, str]
    expected_output_type: str  # json, text, file, etc.
    timeout: int = 30
    interactive: bool = False
    
@dataclass
class ExecutionResult:
    """执行结果"""
    success: bool
    output: str
    error: str
    exit_code: int
    execution_time: float
    artifacts: List[str] = None  # 生成的文件等

class SmartTerminalExecutor:
    """智能终端执行器"""
    
    def __init__(self, workspace_path: str):
        self.workspace_path = Path(workspace_path)
        self.execution_history = []
        self.environment_state = {}
        
    async def intelligent_execute(self, 
                                instruction: str,
                                context: Optional[Dict] = None) -> ExecutionResult:
        """
        智能命令执行
        基于自然语言指令自动生成和执行命令
        
        Args:
            instruction: 自然语言指令
            context: 执行上下文
            
        Returns:
            执行结果
        """
        # 1. 解析指令意图
        intent = self._parse_instruction_intent(instruction)
        
        # 2. 生成命令序列
        commands = await self._generate_command_sequence(intent, context)
        
        # 3. 验证命令安全性
        safe_commands = await self._validate_command_safety(commands)
        
        # 4. 执行命令序列
        results = []
        for cmd_context in safe_commands:
            result = await self._execute_with_monitoring(cmd_context)
            results.append(result)
            
            # 如果命令失败，尝试自动修复
            if not result.success:
                fixed_result = await self._auto_fix_command(cmd_context, result)
                if fixed_result:
                    results.append(fixed_result)
        
        return self._aggregate_results(results)
    
    async def smart_package_management(self, 
                                     action: str,
                                     packages: List[str],
                                     package_manager: str = "auto") -> ExecutionResult:
        """
        智能包管理
        自动检测项目类型并使用合适的包管理器
        """
        # 自动检测包管理器
        if package_manager == "auto":
            package_manager = await self._detect_package_manager()
        
        # 生成包管理命令
        commands = []
        
        if action == "install":
            if package_manager == "npm":
                commands = [f"npm install {' '.join(packages)}"]
            elif package_manager == "pip":
                commands = [f"pip install {' '.join(packages)}"]
            elif package_manager == "cargo":
                commands = [f"cargo add {' '.join(packages)}"]
            # 添加更多包管理器支持
        
        # 执行命令
        results = []
        for cmd in commands:
            context = ExecutionContext(
                command=cmd,
                working_directory=str(self.workspace_path),
                environment=self.environment_state,
                expected_output_type="text"
            )
            result = await self._execute_with_monitoring(context)
            results.append(result)
        
        return self._aggregate_results(results)
    
    async def development_workflow_automation(self, 
                                            workflow_type: str,
                                            parameters: Dict[str, Any]) -> ExecutionResult:
        """
        开发工作流自动化
        
        支持的工作流类型:
        - test: 运行测试
        - build: 构建项目
        - deploy: 部署
        - lint: 代码检查
        - format: 代码格式化
        """
        workflow_commands = {
            "test": await self._generate_test_commands(parameters),
            "build": await self._generate_build_commands(parameters),
            "deploy": await self._generate_deploy_commands(parameters),
            "lint": await self._generate_lint_commands(parameters),
            "format": await self._generate_format_commands(parameters)
        }
        
        commands = workflow_commands.get(workflow_type, [])
        
        # 执行工作流
        results = []
        for cmd_context in commands:
            result = await self._execute_with_monitoring(cmd_context)
            results.append(result)
            
            # 工作流中断处理
            if not result.success and workflow_type in ["build", "test"]:
                break
        
        return self._aggregate_results(results)
    
    async def interactive_debugging(self, 
                                  debug_target: str,
                                  debug_type: str = "python") -> AsyncGenerator[str, str]:
        """
        交互式调试
        支持实时调试会话
        """
        # 启动调试会话
        if debug_type == "python":
            debug_cmd = f"python -m pdb {debug_target}"
        elif debug_type == "node":
            debug_cmd = f"node --inspect-brk {debug_target}"
        else:
            debug_cmd = f"gdb {debug_target}"
        
        # 创建交互式会话
        process = await asyncio.create_subprocess_shell(
            debug_cmd,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=self.workspace_path
        )
        
        # 处理交互式输入输出
        while True:
            try:
                # 读取输出
                output = await asyncio.wait_for(
                    process.stdout.readline(), timeout=1.0
                )
                if output:
                    yield output.decode()
                
                # 等待用户输入
                user_input = yield None
                if user_input:
                    process.stdin.write(f"{user_input}\n".encode())
                    await process.stdin.drain()
                    
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                break
    
    def _parse_instruction_intent(self, instruction: str) -> Dict[str, Any]:
        """解析指令意图"""
        intent = {
            "action": "execute",
            "target": None,
            "parameters": {},
            "safety_level": "safe"
        }
        
        instruction_lower = instruction.lower()
        
        # 识别动作类型
        if any(word in instruction_lower for word in ["install", "add"]):
            intent["action"] = "install"
        elif any(word in instruction_lower for word in ["run", "execute", "start"]):
            intent["action"] = "run"
        elif any(word in instruction_lower for word in ["test", "check"]):
            intent["action"] = "test"
        elif any(word in instruction_lower for word in ["build", "compile"]):
            intent["action"] = "build"
        elif any(word in instruction_lower for word in ["deploy", "publish"]):
            intent["action"] = "deploy"
            intent["safety_level"] = "high_risk"
        
        return intent
    
    async def _detect_package_manager(self) -> str:
        """自动检测包管理器"""
        if (self.workspace_path / "package.json").exists():
            if (self.workspace_path / "package-lock.json").exists():
                return "npm"
            elif (self.workspace_path / "yarn.lock").exists():
                return "yarn"
            elif (self.workspace_path / "pnpm-lock.yaml").exists():
                return "pnpm"
        elif (self.workspace_path / "requirements.txt").exists() or \
             (self.workspace_path / "pyproject.toml").exists():
            return "pip"
        elif (self.workspace_path / "Cargo.toml").exists():
            return "cargo"
        elif (self.workspace_path / "go.mod").exists():
            return "go"
        
        return "unknown"
    
    async def _execute_with_monitoring(self, context: ExecutionContext) -> ExecutionResult:
        """带监控的命令执行"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            process = await asyncio.create_subprocess_shell(
                context.command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=context.working_directory,
                env=context.environment
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=context.timeout
            )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return ExecutionResult(
                success=process.returncode == 0,
                output=stdout.decode(),
                error=stderr.decode(),
                exit_code=process.returncode,
                execution_time=execution_time
            )
            
        except asyncio.TimeoutError:
            return ExecutionResult(
                success=False,
                output="",
                error="Command timed out",
                exit_code=-1,
                execution_time=context.timeout
            )

# MCP工具配置
MCP_TERMINAL_TOOLS = [
    {
        "name": "intelligent_execute",
        "description": "基于自然语言指令智能执行命令",
        "parameters": {
            "type": "object",
            "properties": {
                "instruction": {"type": "string", "description": "自然语言指令"},
                "context": {"type": "object", "description": "执行上下文"}
            },
            "required": ["instruction"]
        }
    },
    {
        "name": "smart_package_management",
        "description": "智能包管理",
        "parameters": {
            "type": "object",
            "properties": {
                "action": {"type": "string", "enum": ["install", "uninstall", "update"]},
                "packages": {"type": "array", "items": {"type": "string"}},
                "package_manager": {"type": "string", "default": "auto"}
            },
            "required": ["action", "packages"]
        }
    },
    {
        "name": "development_workflow_automation",
        "description": "开发工作流自动化",
        "parameters": {
            "type": "object",
            "properties": {
                "workflow_type": {"type": "string", "enum": ["test", "build", "deploy", "lint", "format"]},
                "parameters": {"type": "object"}
            },
            "required": ["workflow_type"]
        }
    }
]

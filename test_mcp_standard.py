#!/usr/bin/env python3
"""
测试符合官方标准的MCP服务器
验证FastMCP实现是否正确
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否正确"""
    print("=== 测试MCP导入 ===")
    
    try:
        from mcp.server.fastmcp import FastMCP
        print("✅ FastMCP导入成功")
        return True
    except ImportError as e:
        print(f"❌ FastMCP导入失败: {e}")
        print("请安装MCP Python SDK: pip install mcp")
        return False

def test_server_creation():
    """测试服务器创建"""
    print("\n=== 测试服务器创建 ===")
    
    try:
        from mcp.server.fastmcp import FastMCP
        
        # 创建测试服务器
        mcp = FastMCP("test-server")
        
        @mcp.tool()
        def test_tool(message: str = "Hello") -> str:
            """测试工具"""
            return f"收到消息: {message}"
        
        print("✅ 服务器创建成功")
        print("✅ 工具注册成功")
        return True
        
    except Exception as e:
        print(f"❌ 服务器创建失败: {e}")
        return False

def test_fengshui_server():
    """测试风水服务器"""
    print("\n=== 测试风水服务器 ===")
    
    try:
        from mcp_servers.fengshui_server import FengshuiAnalysisServer
        
        # 创建服务器实例
        server = FengshuiAnalysisServer()
        print("✅ 风水服务器创建成功")
        
        # 检查数据库初始化
        if os.path.exists(server.db_path):
            print("✅ 数据库文件存在")
        else:
            print("⚠️  数据库文件不存在，但这是正常的")
        
        return True
        
    except Exception as e:
        print(f"❌ 风水服务器测试失败: {e}")
        return False

def test_yijing_server():
    """测试易经服务器"""
    print("\n=== 测试易经服务器 ===")
    
    try:
        from mcp_servers.yijing_server import YijingKnowledgeServer
        
        # 创建服务器实例
        server = YijingKnowledgeServer()
        print("✅ 易经服务器创建成功")
        
        # 检查数据库初始化
        if os.path.exists(server.db_path):
            print("✅ 数据库文件存在")
        else:
            print("⚠️  数据库文件不存在，但这是正常的")
        
        return True
        
    except Exception as e:
        print(f"❌ 易经服务器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 MCP服务器标准化测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_server_creation,
        test_fengshui_server,
        test_yijing_server
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCP服务器符合官方标准")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步修正")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

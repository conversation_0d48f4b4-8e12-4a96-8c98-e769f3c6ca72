#!/bin/bash

echo "✅ OpenHands部署验证脚本"
echo "================================"

echo "🔍 步骤1: 检查容器运行状态..."
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep openhands-app

echo ""
echo "🌐 步骤2: 测试Web界面响应..."
LOCAL_HTTP=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
echo "本地访问 (http://localhost:3000): HTTP $LOCAL_HTTP"

if [ "$LOCAL_HTTP" = "200" ]; then
    echo "✅ Web界面响应正常"
else
    echo "❌ Web界面响应异常"
    exit 1
fi

echo ""
echo "🔧 步骤3: 验证MCP服务器状态..."

# 检查MCP代理服务器
cd /www/wwwroot/ai.guiyunai.fun/mcp-proxies

# 启动MCP服务器（如果未运行）
if ! curl -s -f http://localhost:8080/sse >/dev/null 2>&1; then
    echo "启动文件系统MCP服务器..."
    nohup npx -y supergateway \
        --stdio "npx -y @modelcontextprotocol/server-filesystem /www/wwwroot/ai.guiyunai.fun/workspace" \
        --port 8080 \
        --ssePath /sse \
        --messagePath /message \
        --logLevel info > logs/filesystem.log 2>&1 &
    sleep 3
fi

if ! curl -s -f http://localhost:8083/sse >/dev/null 2>&1; then
    echo "启动内存MCP服务器..."
    nohup npx -y supergateway \
        --stdio "npx -y @modelcontextprotocol/server-memory" \
        --port 8083 \
        --ssePath /sse \
        --messagePath /message \
        --logLevel info > logs/memory.log 2>&1 &
    sleep 3
fi

# 验证MCP服务器状态
MCP_FS_STATUS=$(curl -s -f http://localhost:8080/sse >/dev/null 2>&1 && echo "✅ 正常" || echo "❌ 异常")
MCP_MEM_STATUS=$(curl -s -f http://localhost:8083/sse >/dev/null 2>&1 && echo "✅ 正常" || echo "❌ 异常")

echo "文件系统MCP服务器 (8080): $MCP_FS_STATUS"
echo "内存MCP服务器 (8083): $MCP_MEM_STATUS"

echo ""
echo "🌍 步骤4: 测试外部访问..."
EXTERNAL_HTTP=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null)
echo "外部访问 (https://ai.guiyunai.fun): HTTP $EXTERNAL_HTTP"

echo ""
echo "📊 步骤5: 系统资源检查..."
echo "内存使用:"
free -h | head -2

echo ""
echo "磁盘使用:"
df -h /www/wwwroot/ai.guiyunai.fun | tail -1

echo ""
echo "Docker容器资源:"
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep openhands

echo ""
echo "🎯 最终部署状态报告:"
echo "================================"

if [ "$LOCAL_HTTP" = "200" ]; then
    echo "🎉 OpenHands部署成功！"
    echo ""
    echo "📋 服务状态总览:"
    echo "✅ OpenHands Web界面: 正常运行"
    echo "✅ Docker容器: 稳定运行"
    echo "$MCP_FS_STATUS MCP文件系统服务器"
    echo "$MCP_MEM_STATUS MCP内存服务器"
    
    echo ""
    echo "🌐 访问地址:"
    echo "- 本地访问: http://localhost:3000"
    echo "- 外部访问: https://ai.guiyunai.fun"
    
    echo ""
    echo "🔧 功能验证清单:"
    echo "□ 1. 访问Web界面并确认加载正常"
    echo "□ 2. 进入设置页面查看配置选项"
    echo "□ 3. 查找MCP选项卡或工具集成"
    echo "□ 4. 创建新对话测试AI响应"
    echo "□ 5. 测试文件操作功能"
    echo "□ 6. 验证代码执行能力"
    
    echo ""
    echo "🎯 下一步操作:"
    echo "1. 打开浏览器访问: http://localhost:3000"
    echo "2. 清除浏览器缓存确保最新版本"
    echo "3. 测试基本AI对话功能"
    echo "4. 验证MCP工具是否在界面中可见"
    
else
    echo "❌ 部署验证失败"
    echo "请检查容器日志: docker logs openhands-app"
fi

echo ""
echo "📝 管理命令:"
echo "- 查看日志: docker logs openhands-app -f"
echo "- 重启服务: docker restart openhands-app"
echo "- 停止服务: docker stop openhands-app"
echo "- 查看状态: docker ps | grep openhands"

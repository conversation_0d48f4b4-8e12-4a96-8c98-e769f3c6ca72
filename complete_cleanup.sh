#!/bin/bash

echo "🧹 OpenHands完全清理脚本"
echo "================================"

echo "📊 清理前状态检查..."
echo "当前OpenHands容器:"
docker ps -a | grep openhands || echo "无OpenHands容器"

echo ""
echo "当前MCP代理服务器状态:"
ps aux | grep supergateway | grep -v grep || echo "无MCP代理服务器运行"

echo ""
echo "🛑 步骤1: 停止所有OpenHands容器..."

# 停止主应用容器
if docker ps -q -f name=openhands-app | grep -q .; then
    echo "停止 openhands-app..."
    docker stop openhands-app
else
    echo "openhands-app 未运行"
fi

# 停止所有运行时容器
RUNTIME_CONTAINERS=$(docker ps -q -f name=openhands-runtime)
if [ -n "$RUNTIME_CONTAINERS" ]; then
    echo "停止运行时容器..."
    echo $RUNTIME_CONTAINERS | xargs docker stop
    echo "✅ 运行时容器已停止"
else
    echo "无运行时容器需要停止"
fi

echo ""
echo "🗑️ 步骤2: 删除所有OpenHands容器..."

# 删除主应用容器
if docker ps -aq -f name=openhands-app | grep -q .; then
    echo "删除 openhands-app..."
    docker rm openhands-app
else
    echo "openhands-app 不存在"
fi

# 删除所有运行时容器
ALL_RUNTIME_CONTAINERS=$(docker ps -aq -f name=openhands-runtime)
if [ -n "$ALL_RUNTIME_CONTAINERS" ]; then
    echo "删除运行时容器..."
    echo $ALL_RUNTIME_CONTAINERS | xargs docker rm
    echo "✅ 运行时容器已删除"
else
    echo "无运行时容器需要删除"
fi

echo ""
echo "🧽 步骤3: 清理Docker系统资源..."
echo "清理未使用的容器、网络、镜像和构建缓存..."
docker system prune -f

echo ""
echo "💾 步骤4: 保留重要数据检查..."

# 检查workspace目录
if [ -d "/www/wwwroot/ai.guiyunai.fun/workspace" ]; then
    echo "✅ workspace目录已保留: $(du -sh /www/wwwroot/ai.guiyunai.fun/workspace)"
else
    echo "⚠️ workspace目录不存在，将创建"
    mkdir -p /www/wwwroot/ai.guiyunai.fun/workspace
fi

# 检查MCP代理服务器
echo ""
echo "🔍 检查MCP代理服务器状态..."
if curl -s -f http://localhost:8080/sse >/dev/null 2>&1; then
    echo "✅ 文件系统MCP服务器 (8080): 正常运行"
else
    echo "❌ 文件系统MCP服务器 (8080): 未运行"
fi

if curl -s -f http://localhost:8083/sse >/dev/null 2>&1; then
    echo "✅ 内存MCP服务器 (8083): 正常运行"
else
    echo "❌ 内存MCP服务器 (8083): 未运行"
fi

echo ""
echo "🎯 清理完成状态:"
echo "Docker容器:"
docker ps -a | grep openhands || echo "✅ 无OpenHands容器"

echo ""
echo "Docker镜像 (保留用于重新部署):"
docker images | grep openhands || echo "需要重新拉取镜像"

echo ""
echo "✅ 清理完成！环境已准备好重新部署"

#!/bin/bash

echo "🔄 刷新OpenHands MCP配置"
echo "================================"

echo "📋 当前版本信息:"
docker images | grep openhands

echo ""
echo "🛑 停止OpenHands服务..."
docker stop openhands-app

echo ""
echo "🧹 清理容器缓存..."
docker system prune -f

echo ""
echo "📝 验证MCP配置文件..."
echo "当前MCP配置:"
cat /www/wwwroot/ai.guiyunai.fun/OpenHands/config.toml | grep -A 10 "\[mcp\]"

echo ""
echo "🔍 检查MCP代理服务器状态..."
if curl -s -f http://localhost:8080/sse >/dev/null 2>&1; then
    echo "✅ 文件系统服务器 (8080): 运行正常"
else
    echo "❌ 文件系统服务器 (8080): 需要启动"
    echo "启动文件系统服务器..."
    cd /www/wwwroot/ai.guiyunai.fun/mcp-proxies
    nohup npx -y supergateway \
        --stdio "npx -y @modelcontextprotocol/server-filesystem /www/wwwroot/ai.guiyunai.fun/workspace" \
        --port 8080 \
        --ssePath /sse \
        --messagePath /message \
        --logLevel info > logs/filesystem.log 2>&1 &
fi

if curl -s -f http://localhost:8083/sse >/dev/null 2>&1; then
    echo "✅ 内存服务器 (8083): 运行正常"
else
    echo "❌ 内存服务器 (8083): 需要启动"
    echo "启动内存服务器..."
    cd /www/wwwroot/ai.guiyunai.fun/mcp-proxies
    nohup npx -y supergateway \
        --stdio "npx -y @modelcontextprotocol/server-memory" \
        --port 8083 \
        --ssePath /sse \
        --messagePath /message \
        --logLevel info > logs/memory.log 2>&1 &
fi

echo ""
echo "⏳ 等待MCP服务器启动..."
sleep 5

echo ""
echo "🚀 重启OpenHands容器..."
docker start openhands-app

echo ""
echo "⏳ 等待OpenHands启动..."
sleep 10

echo ""
echo "🔍 检查OpenHands状态..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200"; then
    echo "✅ OpenHands Web界面正常"
else
    echo "❌ OpenHands Web界面异常，检查日志..."
    docker logs openhands-app --tail 10
fi

echo ""
echo "📊 最终状态检查:"
echo "OpenHands容器:"
docker ps | grep openhands-app

echo ""
echo "MCP代理服务器:"
ps aux | grep supergateway | grep -v grep

echo ""
echo "🎯 完成！请执行以下操作:"
echo "1. 清除浏览器缓存 (Ctrl+Shift+R 或 Cmd+Shift+R)"
echo "2. 重新访问: http://localhost:3000"
echo "3. 进入设置页面查看MCP选项卡"
echo "4. 如果仍然看不到MCP选项，请检查浏览器开发者工具的控制台错误"

echo ""
echo "🔧 故障排除:"
echo "- 查看OpenHands日志: docker logs openhands-app"
echo "- 查看MCP日志: ls -la /www/wwwroot/ai.guiyunai.fun/mcp-proxies/logs/"
echo "- 重新配置: ./mcp_manager.sh restart"

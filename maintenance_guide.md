# OpenHands运行时维护指南

## 🎉 修复完成总结

### ✅ 已解决的问题
- **运行时容器启动**: 现在能够正常创建和启动运行时容器
- **环境变量配置**: 添加了完整的SANDBOX配置参数
- **容器通信**: 优化了容器间网络配置和通信
- **启动超时**: 设置了合理的启动超时时间(120秒)
- **容器保持存活**: 配置了运行时容器保持存活策略

### 🔧 关键配置改进
```bash
SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik
SANDBOX_KEEP_RUNTIME_ALIVE=true
SANDBOX_TIMEOUT=300
SANDBOX_RUNTIME_STARTUP_TIMEOUT=120
SANDBOX_USER_ID=1000
```

## 📋 日常维护建议

### 1. 监控检查 (每日)
```bash
# 检查容器状态
docker ps | grep openhands

# 检查服务访问
curl -I https://ai.guiyunai.fun

# 检查系统资源
free -h && df -h
```

### 2. 定期清理 (每周)
```bash
# 清理未使用的Docker资源
docker system prune -f

# 清理旧的运行时容器
docker container prune -f

# 检查磁盘空间
df -h
```

### 3. 更新维护 (每月)
```bash
# 更新OpenHands镜像
docker pull docker.all-hands.dev/all-hands-ai/openhands:0.51
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik

# 重启服务应用更新
docker restart openhands-app
```

## 🚨 故障排除指南

### 问题1: "等待运行时启动"仍然出现
**症状**: 创建新任务时仍显示等待状态
**解决方案**:
```bash
# 1. 检查运行时容器
docker ps | grep runtime

# 2. 如果没有运行时容器，重启主容器
docker restart openhands-app

# 3. 等待2-3分钟后重试
```

### 问题2: 运行时容器启动失败
**症状**: 没有运行时容器在运行
**解决方案**:
```bash
# 1. 检查Docker资源
docker system df

# 2. 清理资源
docker system prune -f

# 3. 重新拉取镜像
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik

# 4. 重启服务
docker restart openhands-app
```

### 问题3: 容器启动缓慢
**症状**: 运行时启动时间超过2分钟
**解决方案**:
```bash
# 1. 检查系统资源
free -h
df -h

# 2. 检查Docker性能
docker stats

# 3. 如果资源不足，考虑升级服务器配置
```

## 🔍 监控脚本

### 健康检查脚本
```bash
#!/bin/bash
# health_check.sh

echo "OpenHands健康检查 $(date)"
echo "=========================="

# 检查主容器
if docker ps | grep -q openhands-app; then
    echo "✅ 主容器: 运行中"
else
    echo "❌ 主容器: 未运行"
fi

# 检查运行时容器
RUNTIME_COUNT=$(docker ps | grep runtime | wc -l)
echo "✅ 运行时容器: $RUNTIME_COUNT 个"

# 检查HTTP服务
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun)
echo "✅ HTTP服务: $HTTP_CODE"

# 检查系统资源
MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
echo "📊 内存使用: ${MEM_USAGE}%"
echo "📊 磁盘使用: ${DISK_USAGE}%"
```

## 🎯 性能优化建议

### 1. 容器资源优化
- 确保服务器至少有4GB可用内存
- 保持至少10GB可用磁盘空间
- 定期清理Docker资源

### 2. 网络优化
- 使用CDN加速静态资源
- 优化Nginx配置
- 启用gzip压缩

### 3. 运行时优化
- 使用`SANDBOX_KEEP_RUNTIME_ALIVE=true`保持容器存活
- 适当调整`SANDBOX_TIMEOUT`值
- 预拉取常用镜像

## 📞 紧急联系

### 快速恢复命令
```bash
# 完全重启OpenHands
docker stop openhands-app
docker rm openhands-app
cd /www/wwwroot/ai.guiyunai.fun
./start_openhands_optimized.sh
```

### 备份恢复
```bash
# 备份配置
cp -r /www/wwwroot/ai.guiyunai.fun/.openhands /backup/

# 恢复配置
cp -r /backup/.openhands /www/wwwroot/ai.guiyunai.fun/
```

## 📈 升级路径

当新版本发布时：
1. 备份当前配置
2. 拉取新镜像
3. 更新启动脚本
4. 测试功能
5. 回滚备份（如有问题）

---

**维护负责人**: 系统管理员
**最后更新**: 2025-08-04
**下次检查**: 2025-08-11

#!/usr/bin/env python3
"""
剧本杀MCP服务器
提供剧本创作、角色设计、游戏机制等全方位剧本杀服务
符合官方MCP协议标准
"""

import json
import os
import random
from typing import Dict, List, Any, Optional
from datetime import datetime

# 使用官方推荐的FastMCP框架
from mcp.server.fastmcp import FastMCP

class ScriptMurderServer:
    """剧本杀服务器类
    
    提供专业的剧本杀服务，包括：
    - 剧本创作和故事构建
    - 角色设计和背景设定
    - 游戏机制和规则设计
    - 线索系统和推理逻辑
    - 主持人指南和玩家体验
    符合官方MCP协议标准
    """

    def __init__(self) -> None:
        """初始化剧本杀服务器"""
        self.scripts_path = os.getenv("SCRIPTS_PATH", "data/scripts")
        self.characters_path = os.getenv("CHARACTERS_PATH", "data/characters")
        self.templates_path = os.getenv("SCRIPT_TEMPLATES_PATH", "data/templates")
        self.language = os.getenv("LANGUAGE", "zh-CN")

        # 确保目录存在
        os.makedirs(self.scripts_path, exist_ok=True)
        os.makedirs(self.characters_path, exist_ok=True)
        os.makedirs(self.templates_path, exist_ok=True)

        # 使用FastMCP创建服务器实例
        self.mcp = FastMCP("script-murder")
        self.setup_tools()

    def setup_tools(self) -> None:
        """设置MCP工具 - 符合官方MCP协议标准"""

        @self.mcp.tool()
        async def create_murder_script(
            theme: str,
            setting: str,
            player_count: int = 6,
            difficulty_level: str = "中等",
            game_duration: str = "3-4小时",
            special_mechanics: str = ""
        ) -> str:
            """
            剧本杀剧本创作工具 - 基于主题和设定创作完整的剧本杀剧本

            适用场景：剧本杀店铺、个人创作、团建活动、娱乐聚会

            Args:
                theme: 剧本主题（古风/现代/民国/欧式/科幻/悬疑/情感/喜剧）
                setting: 故事背景（具体的时间地点和环境描述）
                player_count: 玩家人数（4-12人）
                difficulty_level: 难度等级（简单/中等/困难/专家级）
                game_duration: 游戏时长（2-3小时/3-4小时/4-5小时/5小时以上）
                special_mechanics: 特殊机制（换装/NPC/多时间线/隐藏身份等）

            Returns:
                完整的剧本杀剧本，包含故事背景、角色设定、时间线、线索系统和主持人指南
            """
            result = await self.generate_murder_script(theme, setting, player_count, difficulty_level, game_duration, special_mechanics)
            return json.dumps(result, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        async def design_character_profiles(
            script_theme: str,
            character_count: int,
            relationship_complexity: str = "中等",
            character_types: str = "多样化",
            backstory_depth: str = "详细"
        ) -> str:
            """
            角色档案设计工具 - 为剧本杀创作丰富立体的角色档案

            适用场景：角色扮演、剧本完善、玩家体验优化

            Args:
                script_theme: 剧本主题背景
                character_count: 角色数量
                relationship_complexity: 关系复杂度（简单/中等/复杂/错综复杂）
                character_types: 角色类型（多样化/特定职业/家族成员/同事朋友）
                backstory_depth: 背景深度（简要/详细/深度/史诗级）

            Returns:
                详细的角色档案，包含身份背景、性格特征、关系网络、秘密信息和行为动机
            """
            result = await self.create_character_profiles(script_theme, character_count, relationship_complexity, character_types, backstory_depth)
            return json.dumps(result, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        async def generate_clue_system(
            murder_method: str,
            evidence_types: str = "物证,证言,时间线",
            red_herrings: int = 3,
            difficulty_progression: str = "递进式",
            revelation_timing: str = "分阶段"
        ) -> str:
            """
            线索系统生成工具 - 设计逻辑严密的线索网络和推理路径

            适用场景：推理游戏、逻辑训练、剧本完善、难度调节

            Args:
                murder_method: 作案手法描述
                evidence_types: 证据类型（物证,证言,时间线,动机,机会,手段）
                red_herrings: 红鲱鱼数量（误导线索的数量）
                difficulty_progression: 难度递进（递进式/平行式/跳跃式/混合式）
                revelation_timing: 揭示时机（分阶段/集中式/玩家主导/随机触发）

            Returns:
                完整的线索系统，包含核心线索、误导信息、推理路径和揭示机制
            """
            result = await self.build_clue_system(murder_method, evidence_types, red_herrings, difficulty_progression, revelation_timing)
            return json.dumps(result, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        async def create_game_mechanics(
            game_style: str = "经典推理",
            interaction_rules: str = "自由讨论",
            voting_system: str = "多轮投票",
            time_management: str = "分阶段限时",
            special_features: str = ""
        ) -> str:
            """
            游戏机制设计工具 - 设计平衡有趣的游戏规则和互动机制

            适用场景：游戏规则制定、平衡性调整、体验优化、创新玩法

            Args:
                game_style: 游戏风格（经典推理/情感本/欢乐本/硬核本/沉浸本）
                interaction_rules: 互动规则（自由讨论/轮流发言/私密交流/公开辩论）
                voting_system: 投票机制（多轮投票/一次投票/排除投票/积分制）
                time_management: 时间管理（分阶段限时/总时长控制/自由节奏/紧张节奏）
                special_features: 特色功能（道具使用/技能系统/随机事件/隐藏机制）

            Returns:
                详细的游戏机制方案，包含规则说明、流程设计、平衡机制和特色玩法
            """
            result = await self.design_game_mechanics(game_style, interaction_rules, voting_system, time_management, special_features)
            return json.dumps(result, ensure_ascii=False, indent=2)

        @self.mcp.tool()
        async def generate_host_guide(
            script_complexity: str = "中等",
            player_experience: str = "混合",
            atmosphere_control: str = "标准",
            contingency_planning: str = "完整"
        ) -> str:
            """
            主持人指南生成工具 - 为主持人提供全面的游戏主持指导

            适用场景：主持人培训、游戏质量保证、突发情况处理、体验优化

            Args:
                script_complexity: 剧本复杂度（简单/中等/复杂/专家级）
                player_experience: 玩家经验（新手/混合/老手/专家）
                atmosphere_control: 氛围控制（轻松/标准/紧张/沉浸）
                contingency_planning: 应急预案（基础/完整/详细/专业）

            Returns:
                完整的主持人指南，包含开场引导、节奏控制、氛围营造和问题处理
            """
            result = await self.create_host_guide(script_complexity, player_experience, atmosphere_control, contingency_planning)
            return json.dumps(result, ensure_ascii=False, indent=2)

    async def generate_murder_script(self, theme: str, setting: str, player_count: int, difficulty_level: str, game_duration: str, special_mechanics: str) -> Dict[str, Any]:
        """生成剧本杀剧本"""
        
        # 生成基础故事框架
        story_framework = self._create_story_framework(theme, setting)
        
        # 设计谋杀案件
        murder_case = self._design_murder_case(theme, difficulty_level)
        
        # 创建时间线
        timeline = self._create_timeline(murder_case, game_duration)
        
        script = {
            "剧本信息": {
                "剧本名称": self._generate_script_title(theme, setting),
                "主题风格": theme,
                "故事背景": setting,
                "玩家人数": player_count,
                "难度等级": difficulty_level,
                "游戏时长": game_duration,
                "特殊机制": special_mechanics.split(',') if special_mechanics else []
            },
            "故事背景": {
                "世界观设定": story_framework["world_building"],
                "历史背景": story_framework["historical_context"],
                "地点描述": story_framework["location_details"],
                "社会环境": story_framework["social_context"]
            },
            "案件概述": {
                "受害者信息": murder_case["victim_profile"],
                "死亡时间": murder_case["time_of_death"],
                "死亡地点": murder_case["crime_scene"],
                "作案手法": murder_case["murder_method"],
                "初步线索": murder_case["initial_clues"]
            },
            "时间线": timeline,
            "游戏流程": {
                "开场阶段": self._design_opening_phase(),
                "调查阶段": self._design_investigation_phase(difficulty_level),
                "讨论阶段": self._design_discussion_phase(),
                "投票阶段": self._design_voting_phase(),
                "真相揭示": self._design_revelation_phase()
            },
            "特色元素": {
                "创新点": self._identify_innovation_points(special_mechanics),
                "情感线": self._design_emotional_arcs(theme),
                "反转设计": self._create_plot_twists(difficulty_level),
                "沉浸元素": self._design_immersive_elements(theme)
            }
        }
        
        return script

    async def create_character_profiles(self, script_theme: str, character_count: int, relationship_complexity: str, character_types: str, backstory_depth: str) -> Dict[str, Any]:
        """创建角色档案"""
        
        # 生成角色基础信息
        characters = []
        for i in range(character_count):
            character = self._generate_character_base(i + 1, script_theme, character_types)
            characters.append(character)
        
        # 建立角色关系网络
        relationships = self._build_relationship_network(characters, relationship_complexity)
        
        # 分配秘密和动机
        secrets_and_motives = self._assign_secrets_and_motives(characters, backstory_depth)
        
        character_profiles = {
            "角色概览": {
                "角色总数": character_count,
                "主题背景": script_theme,
                "关系复杂度": relationship_complexity,
                "角色类型": character_types,
                "背景深度": backstory_depth
            },
            "角色档案": {
                f"角色{i+1}": {
                    "基础信息": characters[i],
                    "关系网络": relationships.get(f"角色{i+1}", {}),
                    "秘密信息": secrets_and_motives.get(f"角色{i+1}", {}).get("secrets", []),
                    "行为动机": secrets_and_motives.get(f"角色{i+1}", {}).get("motives", []),
                    "性格特征": self._generate_personality_traits(characters[i]),
                    "行为模式": self._define_behavior_patterns(characters[i])
                }
                for i in range(character_count)
            },
            "关系图谱": self._create_relationship_map(relationships),
            "角色平衡": {
                "嫌疑分布": self._balance_suspicion_levels(characters),
                "信息分配": self._balance_information_distribution(characters),
                "能力互补": self._ensure_complementary_abilities(characters),
                "戏份平衡": self._balance_screen_time(characters)
            }
        }
        
        return character_profiles

    def _create_story_framework(self, theme: str, setting: str) -> Dict[str, str]:
        """创建故事框架"""
        frameworks = {
            "古风": {
                "world_building": "古代中国背景，传统文化浓厚",
                "historical_context": "特定朝代的社会制度和文化背景",
                "location_details": "古典建筑群落，庭院深深",
                "social_context": "等级森严的社会结构"
            },
            "现代": {
                "world_building": "当代都市生活场景",
                "historical_context": "现代社会发展背景",
                "location_details": "现代化建筑和生活空间",
                "social_context": "多元化的现代社会关系"
            },
            "民国": {
                "world_building": "民国时期的社会变迁",
                "historical_context": "新旧文化交融的时代背景",
                "location_details": "中西合璧的建筑风格",
                "social_context": "传统与现代冲突的社会环境"
            }
        }
        return frameworks.get(theme, frameworks["现代"])

    def _design_murder_case(self, theme: str, difficulty: str) -> Dict[str, Any]:
        """设计谋杀案件"""
        return {
            "victim_profile": self._create_victim_profile(theme),
            "time_of_death": self._determine_death_time(),
            "crime_scene": self._describe_crime_scene(theme),
            "murder_method": self._select_murder_method(difficulty),
            "initial_clues": self._generate_initial_clues(difficulty)
        }

    def _generate_script_title(self, theme: str, setting: str) -> str:
        """生成剧本标题"""
        theme_words = {
            "古风": ["花间", "月下", "江湖", "宫廷", "侠客"],
            "现代": ["都市", "夜色", "迷雾", "真相", "秘密"],
            "民国": ["旧梦", "风云", "往事", "传奇", "谜案"]
        }
        words = theme_words.get(theme, ["神秘", "悬案"])
        return f"{random.choice(words)}{random.choice(['之谜', '疑云', '真相', '秘密', '传说'])}"

def main():
    """主函数 - 使用FastMCP启动服务器"""
    server_instance = ScriptMurderServer()
    
    # 使用FastMCP的标准启动方式
    server_instance.mcp.run()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
简单测试MCP服务器
使用标准MCP Python SDK，确保与OpenHands兼容
"""

import asyncio
import json
import sys
from typing import Any, Sequence

# 使用标准MCP库
try:
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        Resource,
        Tool,
        TextContent,
        ImageContent,
        EmbeddedResource,
    )
except ImportError:
    print("错误：需要安装MCP库")
    print("请运行：pip install mcp")
    sys.exit(1)

# 创建服务器实例
server = Server("simple-test-server")

@server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """
    列出可用的工具
    """
    return [
        Tool(
            name="test_echo",
            description="简单的回声测试工具",
            inputSchema={
                "type": "object",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "要回声的消息",
                    },
                },
                "required": ["message"],
            },
        ),
        Tool(
            name="chinese_greeting",
            description="中文问候工具",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "要问候的人的名字",
                    },
                },
                "required": ["name"],
            },
        ),
        Tool(
            name="simple_calculation",
            description="简单计算工具",
            inputSchema={
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["add", "subtract", "multiply", "divide"],
                        "description": "计算操作类型",
                    },
                    "a": {
                        "type": "number",
                        "description": "第一个数字",
                    },
                    "b": {
                        "type": "number",
                        "description": "第二个数字",
                    },
                },
                "required": ["operation", "a", "b"],
            },
        ),
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict[str, Any]) -> list[TextContent]:
    """
    处理工具调用
    """
    if name == "test_echo":
        message = arguments.get("message", "")
        return [
            TextContent(
                type="text",
                text=f"回声：{message}",
            )
        ]
    
    elif name == "chinese_greeting":
        name_arg = arguments.get("name", "朋友")
        return [
            TextContent(
                type="text",
                text=f"你好，{name_arg}！欢迎使用MCP服务器！",
            )
        ]
    
    elif name == "simple_calculation":
        operation = arguments.get("operation")
        a = arguments.get("a", 0)
        b = arguments.get("b", 0)
        
        try:
            if operation == "add":
                result = a + b
            elif operation == "subtract":
                result = a - b
            elif operation == "multiply":
                result = a * b
            elif operation == "divide":
                if b == 0:
                    return [TextContent(type="text", text="错误：除数不能为零")]
                result = a / b
            else:
                return [TextContent(type="text", text="错误：不支持的操作")]
            
            return [
                TextContent(
                    type="text",
                    text=f"计算结果：{a} {operation} {b} = {result}",
                )
            ]
        except Exception as e:
            return [TextContent(type="text", text=f"计算错误：{str(e)}")]
    
    else:
        raise ValueError(f"未知工具：{name}")

async def main():
    """
    主函数 - 启动MCP服务器
    """
    # 使用stdio传输
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())

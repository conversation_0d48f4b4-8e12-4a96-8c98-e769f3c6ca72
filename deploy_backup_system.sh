#!/bin/bash

# OpenHands备份系统一键部署脚本
# 版本: 2.0
# 作者: 系统管理员
# 日期: 2025-08-04

set -e

PROJECT_DIR="/www/wwwroot/ai.guiyunai.fun"
BACKUP_BASE_DIR="/backup/openhands"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 OpenHands备份系统一键部署${NC}"
echo "=================================="

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ 请使用root权限运行此脚本${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 权限检查通过${NC}"

# 步骤1: 创建必要目录
echo ""
echo -e "${BLUE}📁 步骤1: 创建系统目录${NC}"
mkdir -p "$BACKUP_BASE_DIR"
mkdir -p "/etc/openhands-protection"
mkdir -p "/var/log"
echo -e "${GREEN}✅ 目录创建完成${NC}"

# 步骤2: 设置脚本权限
echo ""
echo -e "${BLUE}🔧 步骤2: 设置脚本权限${NC}"
cd "$PROJECT_DIR"
chmod +x openhands_backup_system.sh
chmod +x openhands_rollback_system.sh
chmod +x openhands_config_protection.sh
chmod +x openhands_backup_test.sh
chmod +x openhands_backup_manager.sh
echo -e "${GREEN}✅ 脚本权限设置完成${NC}"

# 步骤3: 初始化保护系统
echo ""
echo -e "${BLUE}🛡️ 步骤3: 初始化配置保护系统${NC}"
./openhands_config_protection.sh --init
echo -e "${GREEN}✅ 保护系统初始化完成${NC}"

# 步骤4: 创建第一个备份
echo ""
echo -e "${BLUE}💾 步骤4: 创建初始备份${NC}"
./openhands_backup_system.sh
echo -e "${GREEN}✅ 初始备份创建完成${NC}"

# 步骤5: 生成配置校验和
echo ""
echo -e "${BLUE}🔐 步骤5: 生成配置文件校验和${NC}"
./openhands_config_protection.sh --generate-checksums
echo -e "${GREEN}✅ 校验和生成完成${NC}"

# 步骤6: 设置定时任务
echo ""
echo -e "${BLUE}⏰ 步骤6: 设置定时备份任务${NC}"
# 检查是否已存在定时任务
if ! crontab -l 2>/dev/null | grep -q "openhands_backup_system.sh"; then
    # 添加每日备份任务
    (crontab -l 2>/dev/null; echo "0 2 * * * cd ${PROJECT_DIR} && ./openhands_backup_system.sh >/dev/null 2>&1") | crontab -
    echo -e "${GREEN}✅ 每日备份任务已设置 (凌晨2点)${NC}"
else
    echo -e "${YELLOW}⚠️ 备份定时任务已存在${NC}"
fi

if ! crontab -l 2>/dev/null | grep -q "openhands_config_protection.sh.*health-check"; then
    # 添加健康检查任务
    (crontab -l 2>/dev/null; echo "0 * * * * cd ${PROJECT_DIR} && ./openhands_config_protection.sh --health-check >/dev/null 2>&1") | crontab -
    echo -e "${GREEN}✅ 健康检查任务已设置 (每小时)${NC}"
else
    echo -e "${YELLOW}⚠️ 健康检查定时任务已存在${NC}"
fi

# 步骤7: 启动监控
echo ""
echo -e "${BLUE}👁️ 步骤7: 启动配置监控${NC}"
./openhands_config_protection.sh --start-monitoring
echo -e "${GREEN}✅ 配置监控已启动${NC}"

# 步骤8: 运行系统测试
echo ""
echo -e "${BLUE}🧪 步骤8: 运行系统验证测试${NC}"
echo "正在运行基础测试..."
./openhands_backup_test.sh --health >/dev/null 2>&1 && echo -e "${GREEN}✅ 健康检查测试通过${NC}" || echo -e "${YELLOW}⚠️ 健康检查测试有警告${NC}"
./openhands_backup_test.sh --protection >/dev/null 2>&1 && echo -e "${GREEN}✅ 配置保护测试通过${NC}" || echo -e "${YELLOW}⚠️ 配置保护测试有警告${NC}"

# 步骤9: 创建快捷方式
echo ""
echo -e "${BLUE}🔗 步骤9: 创建管理快捷方式${NC}"
cat > /usr/local/bin/openhands-backup << EOF
#!/bin/bash
cd ${PROJECT_DIR}
./openhands_backup_manager.sh
EOF
chmod +x /usr/local/bin/openhands-backup
echo -e "${GREEN}✅ 管理快捷方式已创建: openhands-backup${NC}"

# 步骤10: 生成部署报告
echo ""
echo -e "${BLUE}📊 步骤10: 生成部署报告${NC}"

REPORT_FILE="${PROJECT_DIR}/backup_system_deployment_report.md"
cat > "$REPORT_FILE" << EOF
# OpenHands备份系统部署报告

## 部署信息
- 部署时间: $(date)
- 部署版本: 2.0
- 部署路径: ${PROJECT_DIR}
- 备份目录: ${BACKUP_BASE_DIR}

## 部署组件
✅ 备份系统 (openhands_backup_system.sh)
✅ 回滚系统 (openhands_rollback_system.sh)  
✅ 配置保护 (openhands_config_protection.sh)
✅ 测试验证 (openhands_backup_test.sh)
✅ 管理界面 (openhands_backup_manager.sh)

## 系统状态
- 初始备份: $(ls -1 ${BACKUP_BASE_DIR} | grep -E '^[0-9]{8}_[0-9]{6}$' | wc -l) 个
- 最新备份: $(readlink ${BACKUP_BASE_DIR}/latest 2>/dev/null | xargs basename || echo "无")
- 监控状态: $(pgrep -f "openhands_config_protection.*monitor" >/dev/null && echo "运行中" || echo "未运行")

## 定时任务
$(crontab -l | grep -E "(backup|openhands)" || echo "无")

## 快速使用
\`\`\`bash
# 启动管理界面
openhands-backup

# 创建备份
cd ${PROJECT_DIR} && ./openhands_backup_system.sh

# 查看系统状态
cd ${PROJECT_DIR} && ./openhands_config_protection.sh --status
\`\`\`

## 重要文件位置
- 备份目录: ${BACKUP_BASE_DIR}
- 保护配置: /etc/openhands-protection/
- 日志文件: /var/log/openhands_*.log
- 管理脚本: ${PROJECT_DIR}/openhands_*.sh

---
报告生成时间: $(date)
EOF

echo -e "${GREEN}✅ 部署报告已生成: $REPORT_FILE${NC}"

# 最终状态检查
echo ""
echo -e "${BLUE}🔍 最终状态检查${NC}"
echo "=================================="

# 检查备份
BACKUP_COUNT=$(ls -1 "$BACKUP_BASE_DIR" | grep -E '^[0-9]{8}_[0-9]{6}$' | wc -l)
echo "📦 可用备份: $BACKUP_COUNT 个"

# 检查监控
if pgrep -f "openhands_config_protection.*monitor" >/dev/null; then
    echo -e "👁️ 配置监控: ${GREEN}运行中${NC}"
else
    echo -e "👁️ 配置监控: ${RED}未运行${NC}"
fi

# 检查定时任务
CRON_COUNT=$(crontab -l 2>/dev/null | grep -E "(backup|openhands)" | wc -l)
echo "⏰ 定时任务: $CRON_COUNT 个"

# 检查OpenHands服务
if docker ps | grep -q openhands-app; then
    echo -e "🐳 OpenHands服务: ${GREEN}运行中${NC}"
else
    echo -e "🐳 OpenHands服务: ${RED}未运行${NC}"
fi

echo ""
echo -e "${GREEN}🎉 OpenHands备份系统部署完成！${NC}"
echo "=================================="
echo ""
echo -e "${BLUE}📋 下一步操作:${NC}"
echo "1. 运行 'openhands-backup' 启动管理界面"
echo "2. 查看部署报告: cat $REPORT_FILE"
echo "3. 阅读完整文档: cat ${PROJECT_DIR}/OpenHands_Backup_System_Documentation.md"
echo ""
echo -e "${BLUE}🔧 常用命令:${NC}"
echo "- 创建备份: cd ${PROJECT_DIR} && ./openhands_backup_system.sh"
echo "- 查看状态: cd ${PROJECT_DIR} && ./openhands_config_protection.sh --status"
echo "- 回滚系统: cd ${PROJECT_DIR} && ./openhands_rollback_system.sh -i"
echo "- 运行测试: cd ${PROJECT_DIR} && ./openhands_backup_test.sh --all"
echo ""
echo -e "${YELLOW}⚠️ 重要提醒:${NC}"
echo "- 定期检查备份完整性"
echo "- 监控系统资源使用情况"
echo "- 在重要变更前手动创建备份"
echo "- 测试回滚功能确保可用性"

#!/bin/bash

echo "🔧 OpenHands错误修复脚本"
echo "================================"

# 1. 检查当前状态
echo "📊 检查当前Docker容器状态..."
docker ps -a | grep openhands

echo ""
echo "🧹 清理旧的运行时容器..."
# 停止并删除所有运行时容器
docker ps -a | grep openhands-runtime | awk '{print $1}' | xargs -r docker rm -f

echo ""
echo "🔄 重启OpenHands主应用..."
docker restart openhands-app

echo ""
echo "⏳ 等待服务启动..."
sleep 10

echo ""
echo "📋 检查服务状态..."
docker ps | grep openhands

echo ""
echo "🌐 检查端口监听..."
netstat -tlnp | grep :3000 || echo "端口3000未监听"

echo ""
echo "📝 检查最新日志..."
docker logs openhands-app --tail 20

echo ""
echo "✅ 修复完成！"
echo ""
echo "🎯 下一步操作："
echo "1. 清除浏览器缓存 (Ctrl+Shift+R)"
echo "2. 重新访问 http://localhost:3000"
echo "3. 如果仍有问题，检查浏览器控制台错误"
echo ""
echo "💡 如果React错误持续存在："
echo "- 尝试无痕模式访问"
echo "- 检查是否有浏览器扩展冲突"
echo "- 确保没有其他应用占用相关端口"

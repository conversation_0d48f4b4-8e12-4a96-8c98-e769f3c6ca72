# OpenHands前端MCP配置解决方案

## 🔍 问题分析

根据您提供的错误截图，前端显示"sse_servers必须是一个数组"的错误。这表明前端的MCP配置界面期望特定的数据格式。

## 🎯 解决方案

### 方法1: 通过前端界面手动配置

1. **访问设置页面**
   - 在OpenHands前端界面中，查找"设置"或"Settings"选项
   - 寻找"MCP"、"工具"或"外部服务"相关配置

2. **手动添加MCP服务器**
   
   按照以下格式在前端界面中添加：

   **SSE服务器列表**：
   ```
   http://host.docker.internal:8080/sse
   http://host.docker.internal:8081/sse
   http://host.docker.internal:8082/sse
   http://host.docker.internal:8084/sse
   http://host.docker.internal:8090/sse
   http://host.docker.internal:8100/sse
   http://host.docker.internal:8101/sse
   http://host.docker.internal:8110/sse
   ```

   **STDIO服务器配置**：
   ```
   名称: fengshui
   命令: python3
   参数: ["/workspace/mcp_servers/fengshui_server.py"]

   名称: yijing
   命令: python3
   参数: ["/workspace/mcp_servers/yijing_server.py"]
   ```

### 方法2: 清除缓存并重新配置

1. **清除浏览器缓存**
   - 按 Ctrl+Shift+R (或 Cmd+Shift+R) 强制刷新页面
   - 或者清除浏览器缓存和Cookie

2. **重新访问设置页面**
   - 重新打开 https://ai.guiyunai.fun
   - 进入设置页面查看MCP配置

### 方法3: 使用最小化配置

如果前端仍然报错，可以先使用最小化配置测试：

**仅配置3个基础MCP服务器**：
```json
{
  "sse_servers": [
    "http://host.docker.internal:8080/sse",
    "http://host.docker.internal:8081/sse",
    "http://host.docker.internal:8082/sse"
  ],
  "stdio_servers": [
    {
      "name": "fengshui",
      "command": "python3",
      "args": ["/workspace/mcp_servers/fengshui_server.py"]
    }
  ]
}
```

## 🔧 验证MCP服务器状态

在配置前，请确认所有MCP服务器都在正常运行：

```bash
cd /www/wwwroot/ai.guiyunai.fun
./check_mcp_status.sh
```

应该显示所有8个MCP服务器都在运行中。

## 📋 可用的MCP服务器清单

| 端口 | 服务器名称 | 功能描述 | 状态 |
|------|------------|----------|------|
| 8080 | 文件系统 | 安全的文件操作 | ✅ 运行中 |
| 8081 | 内存 | 知识图谱持久化内存 | ✅ 运行中 |
| 8082 | 网页获取 | 网页内容获取和转换 | ✅ 运行中 |
| 8084 | Git | Git仓库操作和管理 | ✅ 运行中 |
| 8090 | 营销推广 | 营销推广和内容创作 | ✅ 运行中 |
| 8100 | 剧本杀 | 剧本创作和角色设计 | ✅ 运行中 |
| 8101 | 设计创意 | 设计创意和视觉内容创作 | ✅ 运行中 |
| 8110 | 编程开发 | 编程开发和技术学习 | ✅ 运行中 |

## 🎯 推荐操作步骤

1. **立即操作**: 在前端设置页面中查找MCP配置选项
2. **逐步添加**: 先添加3个基础服务器，确认工作后再添加其他
3. **测试功能**: 每添加一个服务器后，测试相关功能是否可用
4. **保存配置**: 确保在前端界面中保存配置

## 🔍 故障排除

如果仍然遇到问题：

1. **检查网络连接**: 确保容器可以访问 `host.docker.internal`
2. **查看容器日志**: `docker logs openhands-app --tail 20`
3. **重启服务**: `docker restart openhands-app`
4. **检查端口**: 确保所有MCP服务器端口都在监听

## 📞 技术支持

如果前端界面中找不到MCP配置选项，可能需要：
- 升级OpenHands到最新版本
- 查看官方文档获取最新的配置方法
- 在OpenHands社区寻求帮助

---

**注意**: 所有MCP服务器已经成功部署并运行，只需要在前端正确配置即可使用全部功能。

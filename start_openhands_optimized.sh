#!/bin/bash

echo "🚀 启动优化的OpenHands容器"
echo "================================"

# 清理旧容器
echo "🧹 清理旧容器..."
docker rm -f openhands-app 2>/dev/null || true

# 确保运行时镜像存在
echo "📦 确保运行时镜像存在..."
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p /www/wwwroot/ai.guiyunai.fun/workspace
mkdir -p /www/wwwroot/ai.guiyunai.fun/.openhands
chown -R 1000:1000 /www/wwwroot/ai.guiyunai.fun/workspace
chown -R 1000:1000 /www/wwwroot/ai.guiyunai.fun/.openhands

echo "🚀 启动OpenHands容器（完整配置）..."

# 启动容器，添加完整的环境变量配置
docker run -d \
    --name openhands-app \
    --restart unless-stopped \
    --privileged \
    -p 3000:3000 \
    -v /www/wwwroot/ai.guiyunai.fun/workspace:/workspace \
    -v /www/wwwroot/ai.guiyunai.fun/.openhands:/.openhands \
    -v /www/wwwroot/ai.guiyunai.fun/OpenHands/config_mcp.toml:/app/config.toml \
    -v /var/run/docker.sock:/var/run/docker.sock \
    --add-host host.docker.internal:host-gateway \
    -e SANDBOX_USER_ID=1000 \
    -e LLM_MODEL="groq/llama-3.3-70b-versatile" \
    -e LLM_API_KEY="********************************************************" \
    -e LLM_BASE_URL="https://api.groq.com/openai/v1" \
    -e WORKSPACE_BASE="/workspace" \
    -e RUNTIME="docker" \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE="docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik" \
    -e SANDBOX_KEEP_RUNTIME_ALIVE="true" \
    -e SANDBOX_TIMEOUT="300" \
    -e SANDBOX_ENABLE_AUTO_LINT="false" \
    -e SANDBOX_USE_HOST_NETWORK="false" \
    -e SANDBOX_LOCAL_RUNTIME_URL="http://host.docker.internal" \
    -e SANDBOX_CONTAINER_IMAGE="docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik" \
    -e SANDBOX_RUNTIME_STARTUP_TIMEOUT="120" \
    -e SANDBOX_RUNTIME_EXTRA_DEPS="" \
    -e LOG_LEVEL="INFO" \
    -e DEBUG="false" \
    -e MCP_CONFIG='{"sse_servers":["http://host.docker.internal:8080/sse","http://host.docker.internal:8081/sse","http://host.docker.internal:8082/sse","http://host.docker.internal:8084/sse","http://host.docker.internal:8090/sse","http://host.docker.internal:8100/sse","http://host.docker.internal:8101/sse","http://host.docker.internal:8110/sse"],"stdio_servers":[{"name":"fengshui","command":"python3","args":["/workspace/mcp_servers/fengshui_server.py"]},{"name":"yijing","command":"python3","args":["/workspace/mcp_servers/yijing_server.py"]}]}' \
    docker.all-hands.dev/all-hands-ai/openhands:0.51

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功"
else
    echo "❌ 容器启动失败"
    exit 1
fi

echo ""
echo "⏳ 等待容器完全启动..."

# 等待容器启动
for i in {1..30}; do
    echo "检查容器状态... ($i/30)"
    
    # 检查容器是否运行
    if docker ps | grep -q openhands-app; then
        echo "✅ 容器正在运行"
        
        # 检查HTTP响应
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
        if [ "$HTTP_CODE" = "200" ]; then
            echo "✅ HTTP服务正常 (HTTP $HTTP_CODE)"
            break
        else
            echo "⏳ 等待HTTP服务启动... (HTTP $HTTP_CODE)"
        fi
    else
        echo "❌ 容器未运行"
        docker logs openhands-app --tail 10
        exit 1
    fi
    
    if [ $i -eq 30 ]; then
        echo "❌ 容器启动超时"
        docker logs openhands-app --tail 20
        exit 1
    fi
    
    sleep 5
done

echo ""
echo "🎯 启动完成状态检查:"
echo "================================"

# 检查容器状态
echo "容器状态:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep openhands

# 检查服务状态
LOCAL_HTTP=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
echo "本地HTTP状态: $LOCAL_HTTP"

# 检查环境变量
echo ""
echo "关键环境变量:"
docker exec openhands-app env | grep -E "(SANDBOX|RUNTIME|LLM)" | sort

echo ""
echo "🎉 OpenHands容器启动完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 等待2-3分钟让所有服务完全启动"
echo "2. 访问 https://ai.guiyunai.fun"
echo "3. 创建新对话测试运行时启动"
echo "4. 如果仍有问题，查看实时日志: docker logs openhands-app -f"

echo ""
echo "🔧 管理命令:"
echo "- 查看日志: docker logs openhands-app -f"
echo "- 重启容器: docker restart openhands-app"
echo "- 检查运行时: docker ps | grep runtime"

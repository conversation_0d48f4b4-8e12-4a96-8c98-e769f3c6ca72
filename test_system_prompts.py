#!/usr/bin/env python3
"""
系统提示词配置测试
"""

import json
import os

def test_system_prompts():
    """测试系统提示词配置"""
    
    print("🌟 慧智伙伴系统提示词配置测试")
    print("=" * 60)
    
    # 检查配置文件是否存在
    config_file = "ai_personality_config.json"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件 {config_file} 不存在")
        return
    
    # 加载配置
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"✅ 配置文件加载成功")
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return
    
    # 显示AI身份信息
    identity = config.get("ai_identity", {})
    print(f"\n🤖 AI身份信息:")
    print(f"  名称: {identity.get('name', 'N/A')}")
    print(f"  英文名: {identity.get('english_name', 'N/A')}")
    print(f"  描述: {identity.get('description', 'N/A')}")
    print(f"  使命: {identity.get('core_mission', 'N/A')}")
    
    # 显示人格特征
    traits = config.get("personality_traits", {})
    print(f"\n🎭 人格特征:")
    for trait, info in traits.items():
        if isinstance(info, dict):
            level = info.get('level', 'N/A')
            desc = info.get('description', 'N/A')
            print(f"  {trait}: 等级{level}/10 - {desc}")
    
    # 显示专业能力
    capabilities = config.get("professional_capabilities", {})
    print(f"\n🎯 专业能力模块:")
    
    traditional = capabilities.get("traditional_wisdom", {})
    print(f"  传统文化智慧 ({len(traditional)}个模块):")
    for cap, info in traditional.items():
        level = info.get('expertise_level', 'N/A')
        print(f"    - {cap}: 专业等级{level}/10")
    
    modern = capabilities.get("modern_professional", {})
    print(f"  现代专业能力 ({len(modern)}个模块):")
    for cap, info in modern.items():
        level = info.get('expertise_level', 'N/A')
        print(f"    - {cap}: 专业等级{level}/10")
    
    # 显示沟通风格
    style = config.get("communication_style", {})
    print(f"\n💬 沟通风格:")
    print(f"  语言偏好: {style.get('language_preference', 'N/A')}")
    print(f"  语调特点: {style.get('tone', 'N/A')}")
    print(f"  结构特征: {style.get('structure', 'N/A')}")
    
    # 显示品牌差异化
    brand = config.get("brand_differentiation", {})
    print(f"\n🌟 品牌差异化:")
    print(f"  独特定位: {brand.get('unique_positioning', 'N/A')}")
    advantages = brand.get("core_advantages", [])
    print(f"  核心优势: {', '.join(advantages)}")
    
    # 生成示例系统提示词
    print(f"\n📝 示例系统提示词:")
    print("-" * 40)
    
    system_prompt = f"""
# 慧智伙伴 - 全面智能助手

## 核心身份
您是{identity.get('name', '慧智伙伴')} ({identity.get('english_name', 'WisdomTech Companion')})，
{identity.get('description', '全面智能助手')}。

## 使命愿景
{identity.get('core_mission', '传承智慧、创新未来、成就用户')}

## 专业能力
您具备以下专业领域的深度知识：

### 传统文化智慧
- 易经占卜分析：精通六十四卦象征意义，结合现代情境解读
- 风水堪舆建议：掌握传统风水理论，结合现代建筑学
- 中医养生指导：基于阴阳五行理论的健康分析

### 现代专业能力  
- 商业智能分析：市场分析、商业模式设计、战略规划
- 技术开发助手：软件架构、代码质量、项目管理
- 创意设计工具：品牌策略、用户体验、内容创作

## 交互原则
- 专业而亲和：既有学者的深度，又有朋友的温度
- 文化融合：适当引用古代典籍，用现代语言阐释传统概念
- 结构化思维：核心观点 → 深度分析 → 实践指导

## 独特价值
{brand.get('unique_positioning', '全球首个文化科技融合的智能助手')}
"""
    
    print(system_prompt)
    
    # 显示回应模板
    templates = config.get("response_templates", {})
    if templates:
        print(f"\n🎨 回应模板示例:")
        print("-" * 40)
        greeting = templates.get("greeting", "")
        if greeting:
            print(f"问候模板:\n{greeting}")
    
    print(f"\n✅ 系统提示词配置测试完成！")

if __name__ == "__main__":
    test_system_prompts()

#!/bin/bash

echo "🚀 启动OpenHands前后端服务"
echo "================================"

# 检查当前端口占用情况
echo "📊 检查端口占用情况..."
echo "端口3000状态:"
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200\|404"; then
    echo "✅ 端口3000有服务运行"
else
    echo "❌ 端口3000无服务或异常"
fi

echo ""
echo "🔍 检查Docker容器状态..."
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep openhands

echo ""
echo "🎯 分析问题:"
echo "1. OpenHands容器在运行，但可能内部服务有问题"
echo "2. 需要检查容器内的前后端服务状态"
echo "3. 可能需要重新启动容器内的服务"

echo ""
echo "🔧 解决方案1: 重启OpenHands容器"
echo "停止容器..."
docker stop openhands-app

echo "启动容器..."
docker start openhands-app

echo "等待容器启动..."
sleep 15

echo ""
echo "🔍 检查容器启动后状态..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200"; then
    echo "✅ 容器重启后服务正常"
    echo "🌐 访问地址: http://localhost:3000"
    echo "🌐 外部访问: https://ai.guiyunai.fun"
    exit 0
fi

echo ""
echo "🔧 解决方案2: 检查容器内服务"
echo "进入容器检查服务状态..."

# 检查容器内的进程
echo "容器内运行的进程:"
docker exec openhands-app ps aux | head -10

echo ""
echo "检查容器日志..."
docker logs openhands-app --tail 20

echo ""
echo "🔧 解决方案3: 手动启动前后端服务"

# 检查前端构建是否存在
if [ -d "OpenHands/frontend/build" ]; then
    echo "✅ 前端构建文件存在"
else
    echo "❌ 前端构建文件不存在，需要构建前端"
    echo "构建前端..."
    cd OpenHands/frontend
    npm run build
    cd ../..
fi

echo ""
echo "🔧 解决方案4: 使用独立的前后端服务"
echo "这将在容器外启动前后端服务，使用不同端口避免冲突"

# 创建后端启动脚本
cat << 'EOF' > start_backend_standalone.sh
#!/bin/bash
echo "🔧 启动独立后端服务 (端口8000)..."

# 设置环境变量
export SANDBOX_USER_ID=1000
export LLM_API_KEY="********************************************************"
export LLM_MODEL="groq/llama-3.3-70b-versatile"
export LLM_BASE_URL="https://api.groq.com/openai/v1"
export RUNTIME="docker"
export WORKSPACE_BASE="/www/wwwroot/ai.guiyunai.fun/workspace"

cd /www/wwwroot/ai.guiyunai.fun/OpenHands

# 检查Python环境
if [ -f "/root/.cache/pypoetry/virtualenvs/openhands-ai-qoVAMGzs-py3.12/bin/python" ]; then
    echo "使用Poetry虚拟环境..."
    /root/.cache/pypoetry/virtualenvs/openhands-ai-qoVAMGzs-py3.12/bin/python -m uvicorn openhands.server.listen:app --host 0.0.0.0 --port 8000
else
    echo "使用系统Python..."
    python3 -m uvicorn openhands.server.listen:app --host 0.0.0.0 --port 8000
fi
EOF

# 创建前端启动脚本
cat << 'EOF' > start_frontend_standalone.sh
#!/bin/bash
echo "🔧 启动独立前端服务 (端口4000)..."

cd /www/wwwroot/ai.guiyunai.fun/OpenHands/frontend

# 检查Node.js版本
echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
fi

# 构建前端（如果需要）
if [ ! -d "build" ]; then
    echo "构建前端..."
    npm run build
fi

# 启动前端开发服务器
echo "启动前端开发服务器..."
npm run dev -- --port 4000 --host 0.0.0.0
EOF

chmod +x start_backend_standalone.sh
chmod +x start_frontend_standalone.sh

echo ""
echo "✅ 独立启动脚本已创建！"
echo ""
echo "🎯 现在您有以下选择:"
echo ""
echo "选择1: 使用Docker容器 (推荐)"
echo "  访问: http://localhost:3000"
echo "  如果502错误，请检查容器日志: docker logs openhands-app"
echo ""
echo "选择2: 使用独立服务 (调试用)"
echo "  后端: ./start_backend_standalone.sh (端口8000)"
echo "  前端: ./start_frontend_standalone.sh (端口4000)"
echo "  访问: http://localhost:4000"
echo ""
echo "选择3: 重新部署容器"
echo "  docker stop openhands-app && docker rm openhands-app"
echo "  然后重新运行部署脚本"

echo ""
echo "🔍 当前状态检查:"
curl -s -o /dev/null -w "Docker容器服务(3000): HTTP %{http_code}\n" http://localhost:3000 || echo "Docker容器服务(3000): 连接失败"
echo ""
echo "📋 建议的下一步:"
echo "1. 先尝试访问 http://localhost:3000"
echo "2. 如果仍然502，运行: docker logs openhands-app --tail 50"
echo "3. 如果需要调试，可以使用独立服务模式"

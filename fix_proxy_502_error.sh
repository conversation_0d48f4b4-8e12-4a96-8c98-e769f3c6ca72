#!/bin/bash

echo "🔧 修复502 Bad Gateway错误"
echo "================================"

# 1. 检查OpenHands服务状态
echo "📊 检查OpenHands服务状态..."
echo "Docker容器状态:"
docker ps | grep openhands

echo ""
echo "端口监听状态:"
netstat -tlnp | grep :3000

# 2. 检查Web服务器
echo ""
echo "🌐 检查Web服务器..."
echo "Nginx进程:"
ps aux | grep nginx | grep -v grep || echo "Nginx未运行"

echo ""
echo "Apache进程:"
ps aux | grep apache | grep -v grep || echo "Apache未运行"

echo ""
echo "端口80/443状态:"
netstat -tlnp | grep -E ":(80|443)" || echo "端口80/443未监听"

# 3. 测试本地连接
echo ""
echo "🧪 测试本地连接..."
echo "测试localhost:3000:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 || echo "连接失败"

# 4. 检查防火墙
echo ""
echo "🔥 检查防火墙状态..."
ufw status 2>/dev/null || echo "UFW未安装"
iptables -L INPUT | grep -E "(3000|ACCEPT|DROP)" | head -5 || echo "iptables检查完成"

# 5. 检查可能的反向代理配置
echo ""
echo "📁 查找反向代理配置文件..."
find /etc -name "*.conf" -type f 2>/dev/null | grep -E "(nginx|apache|httpd)" | head -5 || echo "未找到标准配置文件"

# 6. 检查宝塔面板（如果存在）
echo ""
echo "🎛️ 检查宝塔面板..."
if [ -d "/www/server" ]; then
    echo "检测到宝塔面板"
    ls -la /www/server/panel/vhost/nginx/ 2>/dev/null | grep ai.guiyunai.fun || echo "未找到域名配置"
else
    echo "未检测到宝塔面板"
fi

# 7. 生成临时解决方案
echo ""
echo "🚀 生成解决方案..."

cat << 'EOF' > temp_nginx_config.conf
# 临时Nginx配置 - ai.guiyunai.fun
server {
    listen 80;
    server_name ai.guiyunai.fun;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ai.guiyunai.fun;
    
    # SSL配置（需要替换为实际证书路径）
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # OpenHands主应用代理
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # WebSocket支持
    location /socket.io/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

echo "✅ 临时配置文件已生成: temp_nginx_config.conf"

echo ""
echo "🎯 解决方案："
echo "1. 【立即解决】直接访问: http://localhost:3000"
echo "2. 【临时方案】使用IP访问: http://$(curl -s ifconfig.me):3000"
echo "3. 【永久修复】配置反向代理:"
echo "   - 如果使用宝塔面板，在网站管理中添加反向代理"
echo "   - 如果使用Nginx，复制temp_nginx_config.conf到配置目录"
echo "   - 重启Web服务器"

echo ""
echo "💡 快速测试命令："
echo "curl -I http://localhost:3000  # 测试本地连接"
echo "curl -I https://ai.guiyunai.fun  # 测试域名连接"

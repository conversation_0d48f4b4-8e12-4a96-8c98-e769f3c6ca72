#!/bin/bash

# OpenHands配置保护和监控系统
# 版本: 2.0
# 作者: 系统管理员
# 日期: 2025-08-04

set -e

# 配置变量
PROJECT_DIR="/www/wwwroot/ai.guiyunai.fun"
PROTECTION_DIR="/etc/openhands-protection"
BACKUP_BASE_DIR="/backup/openhands"
LOG_FILE="/var/log/openhands_protection.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

# 初始化保护系统
init_protection() {
    log "初始化配置保护系统..."
    
    # 创建保护目录
    mkdir -p "$PROTECTION_DIR"/{checksums,readonly,monitoring}
    
    # 创建关键文件列表
    cat > "$PROTECTION_DIR/critical_files.txt" << EOF
/etc/nginx/sites-available/ai.guiyunai.fun
/etc/nginx/nginx.conf
${PROJECT_DIR}/OpenHands/config.toml
${PROJECT_DIR}/start_openhands_optimized.sh
${PROJECT_DIR}/mcp_servers/fengshui_server.py
${PROJECT_DIR}/mcp_servers/yijing_server.py
${PROJECT_DIR}/.openhands/settings.json
EOF

    # 创建监控配置
    cat > "$PROTECTION_DIR/monitoring/config.conf" << EOF
# OpenHands配置监控设置
CHECK_INTERVAL=300  # 5分钟检查一次
ALERT_EMAIL=""      # 告警邮箱
AUTO_BACKUP=true    # 自动备份变更
AUTO_ROLLBACK=false # 自动回滚（谨慎启用）
EOF

    log "保护系统初始化完成"
}

# 生成文件校验和
generate_checksums() {
    log "生成关键文件校验和..."
    
    local checksum_file="$PROTECTION_DIR/checksums/$(date +%Y%m%d_%H%M%S).md5"
    
    while IFS= read -r file; do
        if [ -f "$file" ]; then
            md5sum "$file" >> "$checksum_file"
        else
            warning "文件不存在: $file"
        fi
    done < "$PROTECTION_DIR/critical_files.txt"
    
    # 创建最新校验和链接
    ln -sf "$checksum_file" "$PROTECTION_DIR/checksums/latest.md5"
    
    log "校验和生成完成: $checksum_file"
}

# 验证文件完整性
verify_integrity() {
    log "验证文件完整性..."
    
    local latest_checksum="$PROTECTION_DIR/checksums/latest.md5"
    
    if [ ! -f "$latest_checksum" ]; then
        warning "没有找到基准校验和，生成新的校验和"
        generate_checksums
        return 0
    fi
    
    local changes_detected=false
    local temp_file=$(mktemp)
    
    while IFS= read -r line; do
        local expected_hash=$(echo "$line" | awk '{print $1}')
        local file_path=$(echo "$line" | awk '{print $2}')
        
        if [ -f "$file_path" ]; then
            local current_hash=$(md5sum "$file_path" | awk '{print $1}')
            
            if [ "$expected_hash" != "$current_hash" ]; then
                error "文件已变更: $file_path"
                echo "变更文件: $file_path" >> "$temp_file"
                changes_detected=true
            fi
        else
            error "文件已删除: $file_path"
            echo "删除文件: $file_path" >> "$temp_file"
            changes_detected=true
        fi
    done < "$latest_checksum"
    
    if [ "$changes_detected" = true ]; then
        warning "检测到配置文件变更！"
        cat "$temp_file"
        
        # 触发告警
        trigger_alert "配置文件变更检测" "$temp_file"
        
        # 自动备份（如果启用）
        if grep -q "AUTO_BACKUP=true" "$PROTECTION_DIR/monitoring/config.conf" 2>/dev/null; then
            log "自动触发备份..."
            bash "${PROJECT_DIR}/openhands_backup_system.sh"
        fi
    else
        log "文件完整性验证通过"
    fi
    
    rm -f "$temp_file"
}

# 设置只读保护
set_readonly_protection() {
    log "设置关键文件只读保护..."
    
    while IFS= read -r file; do
        if [ -f "$file" ]; then
            # 备份原始权限
            stat -c "%a %n" "$file" >> "$PROTECTION_DIR/readonly/original_permissions.txt"
            
            # 设置只读
            chmod 444 "$file"
            chattr +i "$file" 2>/dev/null || true  # 设置不可变属性（如果支持）
            
            log "已保护文件: $file"
        fi
    done < "$PROTECTION_DIR/critical_files.txt"
}

# 移除只读保护
remove_readonly_protection() {
    log "移除只读保护..."
    
    while IFS= read -r file; do
        if [ -f "$file" ]; then
            chattr -i "$file" 2>/dev/null || true  # 移除不可变属性
            
            # 恢复原始权限
            local original_perm=$(grep "$file" "$PROTECTION_DIR/readonly/original_permissions.txt" | awk '{print $1}')
            if [ ! -z "$original_perm" ]; then
                chmod "$original_perm" "$file"
            else
                chmod 644 "$file"  # 默认权限
            fi
            
            log "已解除保护: $file"
        fi
    done < "$PROTECTION_DIR/critical_files.txt"
    
    # 清空权限记录
    > "$PROTECTION_DIR/readonly/original_permissions.txt"
}

# 触发告警
trigger_alert() {
    local alert_type="$1"
    local details_file="$2"
    
    log "触发告警: $alert_type"
    
    # 记录告警
    local alert_file="$PROTECTION_DIR/monitoring/alerts_$(date +%Y%m%d).log"
    {
        echo "=========================================="
        echo "告警时间: $(date)"
        echo "告警类型: $alert_type"
        echo "详细信息:"
        cat "$details_file" 2>/dev/null || echo "无详细信息"
        echo "=========================================="
    } >> "$alert_file"
    
    # 发送邮件告警（如果配置了邮箱）
    local alert_email=$(grep "ALERT_EMAIL=" "$PROTECTION_DIR/monitoring/config.conf" | cut -d'=' -f2 | tr -d '"')
    if [ ! -z "$alert_email" ]; then
        {
            echo "OpenHands系统告警"
            echo "=================="
            echo "告警类型: $alert_type"
            echo "告警时间: $(date)"
            echo ""
            echo "详细信息:"
            cat "$details_file" 2>/dev/null || echo "无详细信息"
        } | mail -s "OpenHands系统告警: $alert_type" "$alert_email" 2>/dev/null || true
    fi
}

# 系统健康检查
health_check() {
    log "执行系统健康检查..."
    
    local issues_file=$(mktemp)
    local issues_count=0
    
    # 检查OpenHands容器
    if ! docker ps | grep -q openhands-app; then
        echo "OpenHands容器未运行" >> "$issues_file"
        ((issues_count++))
    fi
    
    # 检查运行时容器
    local runtime_count=$(docker ps | grep runtime | wc -l)
    if [ $runtime_count -eq 0 ]; then
        echo "运行时容器未运行" >> "$issues_file"
        ((issues_count++))
    fi
    
    # 检查HTTP服务
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
    if [ "$http_code" != "200" ]; then
        echo "HTTP服务异常 (HTTP $http_code)" >> "$issues_file"
        ((issues_count++))
    fi
    
    # 检查Nginx配置
    if ! nginx -t >/dev/null 2>&1; then
        echo "Nginx配置错误" >> "$issues_file"
        ((issues_count++))
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ $disk_usage -gt 90 ]; then
        echo "磁盘空间不足 (${disk_usage}%)" >> "$issues_file"
        ((issues_count++))
    fi
    
    # 检查内存使用
    local mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ $mem_usage -gt 90 ]; then
        echo "内存使用过高 (${mem_usage}%)" >> "$issues_file"
        ((issues_count++))
    fi
    
    if [ $issues_count -gt 0 ]; then
        error "检测到 $issues_count 个系统问题"
        trigger_alert "系统健康检查失败" "$issues_file"
        
        # 自动回滚（如果启用且问题严重）
        if [ $issues_count -ge 3 ] && grep -q "AUTO_ROLLBACK=true" "$PROTECTION_DIR/monitoring/config.conf" 2>/dev/null; then
            warning "问题严重，触发自动回滚..."
            bash "${PROJECT_DIR}/openhands_rollback_system.sh" -q
        fi
    else
        log "系统健康检查通过"
    fi
    
    rm -f "$issues_file"
}

# 启动监控守护进程
start_monitoring() {
    log "启动配置监控守护进程..."
    
    # 检查是否已经在运行
    if pgrep -f "openhands_config_protection.*monitor" >/dev/null; then
        warning "监控进程已在运行"
        return 0
    fi
    
    # 后台运行监控
    nohup bash "$0" --monitor-daemon >/dev/null 2>&1 &
    local pid=$!
    
    echo $pid > "$PROTECTION_DIR/monitoring/monitor.pid"
    log "监控守护进程已启动 (PID: $pid)"
}

# 停止监控守护进程
stop_monitoring() {
    log "停止配置监控守护进程..."
    
    local pid_file="$PROTECTION_DIR/monitoring/monitor.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill "$pid" 2>/dev/null; then
            log "监控进程已停止 (PID: $pid)"
        else
            warning "无法停止监控进程 (PID: $pid)"
        fi
        rm -f "$pid_file"
    else
        warning "监控进程PID文件不存在"
    fi
}

# 监控守护进程主循环
monitor_daemon() {
    log "配置监控守护进程启动"
    
    # 读取配置
    local check_interval=300  # 默认5分钟
    if [ -f "$PROTECTION_DIR/monitoring/config.conf" ]; then
        source "$PROTECTION_DIR/monitoring/config.conf"
        check_interval=${CHECK_INTERVAL:-300}
    fi
    
    while true; do
        verify_integrity
        health_check
        sleep $check_interval
    done
}

# 显示状态
show_status() {
    echo "🔒 OpenHands配置保护系统状态"
    echo "============================="
    
    # 监控状态
    if pgrep -f "openhands_config_protection.*monitor" >/dev/null; then
        echo "✅ 监控守护进程: 运行中"
    else
        echo "❌ 监控守护进程: 未运行"
    fi
    
    # 保护状态
    local protected_count=0
    while IFS= read -r file; do
        if [ -f "$file" ] && [ ! -w "$file" ]; then
            ((protected_count++))
        fi
    done < "$PROTECTION_DIR/critical_files.txt" 2>/dev/null || true
    
    echo "🛡️ 受保护文件: $protected_count"
    
    # 最近备份
    if [ -L "$BACKUP_BASE_DIR/latest" ]; then
        local latest_backup=$(readlink "$BACKUP_BASE_DIR/latest")
        local backup_name=$(basename "$latest_backup")
        echo "💾 最新备份: $backup_name"
    else
        echo "💾 最新备份: 无"
    fi
    
    # 最近告警
    local today_alerts="$PROTECTION_DIR/monitoring/alerts_$(date +%Y%m%d).log"
    if [ -f "$today_alerts" ]; then
        local alert_count=$(grep -c "告警时间:" "$today_alerts")
        echo "⚠️ 今日告警: $alert_count 次"
    else
        echo "⚠️ 今日告警: 0 次"
    fi
}

# 显示帮助
show_help() {
    cat << EOF
OpenHands配置保护系统使用说明
============================

用法: $0 [选项]

选项:
  --init              初始化保护系统
  --generate-checksums 生成文件校验和
  --verify            验证文件完整性
  --protect           启用只读保护
  --unprotect         移除只读保护
  --health-check      执行系统健康检查
  --start-monitoring  启动监控守护进程
  --stop-monitoring   停止监控守护进程
  --status            显示系统状态
  --monitor-daemon    监控守护进程（内部使用）
  -h, --help          显示此帮助信息

示例:
  $0 --init                    # 初始化保护系统
  $0 --protect                 # 启用文件保护
  $0 --start-monitoring        # 启动监控
  $0 --status                  # 查看状态

注意事项:
- 启用只读保护后，需要先解除保护才能修改文件
- 监控守护进程会定期检查文件完整性和系统健康
- 建议在系统稳定后启用自动回滚功能
EOF
}

# 主函数
main() {
    case "${1:-}" in
        --init)
            init_protection
            ;;
        --generate-checksums)
            generate_checksums
            ;;
        --verify)
            verify_integrity
            ;;
        --protect)
            set_readonly_protection
            ;;
        --unprotect)
            remove_readonly_protection
            ;;
        --health-check)
            health_check
            ;;
        --start-monitoring)
            start_monitoring
            ;;
        --stop-monitoring)
            stop_monitoring
            ;;
        --monitor-daemon)
            monitor_daemon
            ;;
        --status)
            show_status
            ;;
        -h|--help)
            show_help
            ;;
        "")
            echo "请指定操作选项，使用 -h 查看帮助"
            show_help
            ;;
        *)
            error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

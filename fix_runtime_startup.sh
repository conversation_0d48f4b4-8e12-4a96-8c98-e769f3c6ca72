#!/bin/bash

echo "🚀 修复OpenHands运行时启动问题 v2"
echo "=================================="

# 1. 强制清理所有运行时容器
echo "🧹 清理所有运行时容器..."
docker ps -a --format "table {{.Names}}" | grep openhands-runtime | xargs -r docker rm -f

# 2. 清理悬挂的镜像和网络
echo "🗑️ 清理Docker资源..."
docker system prune -f

# 3. 重启OpenHands主应用
echo "🔄 重启OpenHands主应用..."
docker restart openhands-app

# 4. 等待服务完全启动
echo "⏳ 等待服务启动..."
sleep 15

# 5. 检查服务状态
echo "📊 检查服务状态..."
echo "主应用状态:"
docker ps | grep openhands-app

echo ""
echo "运行时容器:"
docker ps | grep runtime || echo "暂无运行时容器"

# 6. 检查端口
echo ""
echo "🌐 检查端口状态..."
netstat -tlnp | grep :3000 || echo "端口3000未监听"

# 7. 检查最新日志
echo ""
echo "📝 最新日志 (最后20行):"
docker logs openhands-app --tail 20

echo ""
echo "✅ 修复完成！"
echo ""
echo "🎯 下一步操作："
echo "1. 刷新浏览器页面"
echo "2. 创建新对话"
echo "3. 如果仍显示'等待运行时启动'，请等待1-2分钟"
echo ""
echo "💡 如果问题持续："
echo "- 检查系统资源 (内存/磁盘空间)"
echo "- 重启Docker服务: sudo systemctl restart docker"
echo "- 完全重启OpenHands: docker-compose down && docker-compose up -d"

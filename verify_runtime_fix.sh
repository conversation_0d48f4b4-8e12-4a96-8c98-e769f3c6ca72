#!/bin/bash

echo "🔍 验证OpenHands运行时修复效果"
echo "================================"

echo "✅ 修复完成状态报告:"
echo ""

# 检查主容器
MAIN_STATUS=$(docker ps | grep openhands-app | wc -l)
if [ $MAIN_STATUS -eq 1 ]; then
    echo "✅ 主容器: 正常运行"
    docker ps --format "   {{.Names}}: {{.Status}}" | grep openhands-app
else
    echo "❌ 主容器: 未运行"
fi

echo ""

# 检查运行时容器
RUNTIME_STATUS=$(docker ps | grep runtime | wc -l)
if [ $RUNTIME_STATUS -ge 1 ]; then
    echo "✅ 运行时容器: 正常运行 ($RUNTIME_STATUS 个)"
    docker ps --format "   {{.Names}}: {{.Status}}" | grep runtime
else
    echo "❌ 运行时容器: 未运行"
fi

echo ""

# 检查HTTP服务
HTTP_LOCAL=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
HTTP_DOMAIN=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null)

echo "🌐 服务访问状态:"
if [ "$HTTP_LOCAL" = "200" ]; then
    echo "✅ 本地访问: HTTP $HTTP_LOCAL"
else
    echo "❌ 本地访问: HTTP $HTTP_LOCAL"
fi

if [ "$HTTP_DOMAIN" = "200" ]; then
    echo "✅ 域名访问: HTTP $HTTP_DOMAIN"
else
    echo "❌ 域名访问: HTTP $HTTP_DOMAIN"
fi

echo ""

# 检查关键环境变量
echo "🔧 关键配置检查:"
SANDBOX_VARS=$(docker exec openhands-app env | grep -E "SANDBOX_RUNTIME_CONTAINER_IMAGE|SANDBOX_KEEP_RUNTIME_ALIVE|SANDBOX_TIMEOUT" | wc -l)
if [ $SANDBOX_VARS -ge 3 ]; then
    echo "✅ SANDBOX环境变量: 配置完整"
else
    echo "❌ SANDBOX环境变量: 配置不完整"
fi

echo ""

# 检查最近的日志
echo "📋 最近的容器日志:"
docker logs openhands-app --tail 5 | grep -E "(Agent session start|AgentState|ERROR|WARNING)" | tail -3

echo ""
echo "🎯 修复效果总结:"
echo "================================"

if [ $MAIN_STATUS -eq 1 ] && [ $RUNTIME_STATUS -ge 1 ] && [ "$HTTP_DOMAIN" = "200" ]; then
    echo "🎉 修复成功！"
    echo ""
    echo "✅ 已解决的问题:"
    echo "- 运行时容器现在能够正常启动"
    echo "- 添加了完整的SANDBOX环境变量配置"
    echo "- 优化了容器启动参数"
    echo "- 配置了运行时保持存活策略"
    
    echo ""
    echo "📋 测试步骤:"
    echo "1. 访问 https://ai.guiyunai.fun"
    echo "2. 点击'让我们开始构建！'创建新对话"
    echo "3. 观察是否还显示'等待运行时启动'"
    echo "4. 尝试发送消息测试AI响应"
    
    echo ""
    echo "⏱️ 预期效果:"
    echo "- 新任务创建时间: 30-60秒（而非无限等待）"
    echo "- 运行时启动状态: 应该能正常完成"
    echo "- AI对话功能: 应该能正常工作"
    
else
    echo "⚠️ 修复可能不完整"
    echo ""
    echo "需要检查的项目:"
    if [ $MAIN_STATUS -ne 1 ]; then
        echo "- 主容器状态异常"
    fi
    if [ $RUNTIME_STATUS -lt 1 ]; then
        echo "- 运行时容器未启动"
    fi
    if [ "$HTTP_DOMAIN" != "200" ]; then
        echo "- 域名访问异常"
    fi
    
    echo ""
    echo "建议操作:"
    echo "1. 查看详细日志: docker logs openhands-app -f"
    echo "2. 重启容器: docker restart openhands-app"
    echo "3. 检查系统资源: free -h && df -h"
fi

echo ""
echo "🔧 管理命令:"
echo "- 查看实时日志: docker logs openhands-app -f"
echo "- 查看运行时日志: docker logs \$(docker ps | grep runtime | awk '{print \$1}') -f"
echo "- 重启服务: docker restart openhands-app"
echo "- 检查容器: docker ps | grep openhands"

echo ""
echo "🆘 如果问题持续:"
echo "1. 等待3-5分钟让运行时完全启动"
echo "2. 清除浏览器缓存并刷新页面"
echo "3. 尝试创建新对话而不是使用旧对话"
echo "4. 检查服务器资源使用情况"

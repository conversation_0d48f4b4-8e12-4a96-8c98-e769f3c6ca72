#!/bin/bash

echo "🚀 启动OpenHands后端服务 (独立模式)"
echo "================================"

# 停止Docker容器以避免端口冲突
echo "停止Docker容器..."
docker stop openhands-app 2>/dev/null || true

echo ""
echo "🔧 设置环境变量..."
export SANDBOX_USER_ID=1000
export LLM_API_KEY="********************************************************"
export LLM_MODEL="groq/llama-3.3-70b-versatile"
export LLM_BASE_URL="https://api.groq.com/openai/v1"
export RUNTIME="docker"
export WORKSPACE_BASE="/www/wwwroot/ai.guiyunai.fun/workspace"

echo "✅ 环境变量设置完成"
echo "LLM模型: $LLM_MODEL"
echo "工作空间: $WORKSPACE_BASE"

echo ""
echo "📁 切换到OpenHands目录..."
cd /www/wwwroot/ai.guiyunai.fun/OpenHands

echo ""
echo "🔍 检查Python环境..."
if [ -f "/root/.cache/pypoetry/virtualenvs/openhands-ai-qoVAMGzs-py3.12/bin/python" ]; then
    echo "✅ 找到Poetry虚拟环境"
    PYTHON_CMD="/root/.cache/pypoetry/virtualenvs/openhands-ai-qoVAMGzs-py3.12/bin/python"
elif command -v python3 &> /dev/null; then
    echo "✅ 使用系统Python3"
    PYTHON_CMD="python3"
else
    echo "❌ 未找到Python环境"
    exit 1
fi

echo "Python路径: $PYTHON_CMD"
echo "Python版本: $($PYTHON_CMD --version)"

echo ""
echo "📦 检查依赖..."
if $PYTHON_CMD -c "import openhands" 2>/dev/null; then
    echo "✅ OpenHands模块已安装"
else
    echo "❌ OpenHands模块未安装，尝试安装..."
    $PYTHON_CMD -m pip install -e .
fi

echo ""
echo "🚀 启动后端服务 (端口3000)..."
echo "访问地址: http://localhost:3000"
echo "按 Ctrl+C 停止服务"
echo ""

# 启动服务
$PYTHON_CMD -m uvicorn openhands.server.listen:app --host 0.0.0.0 --port 3000 --reload

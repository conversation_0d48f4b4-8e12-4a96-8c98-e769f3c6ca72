#!/bin/bash

# OpenHands 恢复脚本 - 20250801_102531
# 用于恢复到备份时的状态

set -e

echo "🔄 恢复OpenHands到 20250801_102531 状态"
echo "=================================="

OPENHANDS_DIR="$HOME/.openhands"
BACKUP_DIR="$(dirname "$0")"

# 停止现有服务
echo "🛑 停止现有服务..."
docker stop openhands-app 2>/dev/null || true
docker rm openhands-app 2>/dev/null || true

# 备份当前配置
if [ -f "$OPENHANDS_DIR/settings.json" ]; then
    cp "$OPENHANDS_DIR/settings.json" "$OPENHANDS_DIR/settings.json.backup.$(date +%Y%m%d_%H%M%S)"
    echo "✅ 当前配置已备份"
fi

# 恢复配置文件
if [ -f "$BACKUP_DIR/openhands_backup_20250801_102531_settings.json" ]; then
    mkdir -p "$OPENHANDS_DIR"
    cp "$BACKUP_DIR/openhands_backup_20250801_102531_settings.json" "$OPENHANDS_DIR/settings.json"
    echo "✅ 配置文件已恢复"
    
    # 提醒用户检查API密钥
    if grep -q "YOUR_DEEPSEEK_API_KEY_HERE" "$OPENHANDS_DIR/settings.json"; then
        echo "⚠️  请更新API密钥在: $OPENHANDS_DIR/settings.json"
    fi
else
    echo "❌ 配置备份文件不存在"
fi

# 恢复会话数据
if [ -f "$BACKUP_DIR/openhands_backup_20250801_102531_sessions.tar.gz" ]; then
    tar -xzf "$BACKUP_DIR/openhands_backup_20250801_102531_sessions.tar.gz" -C "$OPENHANDS_DIR/"
    echo "✅ 会话数据已恢复"
fi

# 启动服务
echo "🚀 启动OpenHands服务..."
cd /www/wwwroot/ai.guiyunai.fun
./scripts/deploy.sh restart

echo "🎉 恢复完成！"
echo "🌐 访问地址: https://ai.guiyunai.fun"

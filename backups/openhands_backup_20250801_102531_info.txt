OpenHands 备份信息
================

备份时间: 20250801_102531
备份位置: /www/wwwroot/ai.guiyunai.fun/backups
系统信息: Linux ser424119180233 5.15.0-151-generic #161-Ubuntu SMP Tue Jul 22 14:25:40 UTC 2025 x86_64 x86_64 x86_64 GNU/Linux
Docker版本: Docker version 28.3.3, build 980b856

包含文件:
- openhands_backup_20250801_102531_settings.json (完整配置)
- openhands_backup_20250801_102531_settings_template.json (模板配置)
- openhands_backup_20250801_102531_sessions.tar.gz (会话数据)
- openhands_backup_20250801_102531_docker_images.txt (Docker镜像信息)
- openhands_backup_20250801_102531_docker_containers.txt (Docker容器信息)
- openhands_backup_20250801_102531_restore.sh (恢复脚本)

恢复方法:
1. 运行恢复脚本: ./openhands_backup_20250801_102531_restore.sh
2. 或手动复制配置文件到 ~/.openhands/settings.json
3. 运行部署脚本: ./scripts/deploy.sh restart

注意事项:
- 请确保API密钥已正确配置
- 恢复前会自动备份当前配置
- 如有问题请检查Docker服务状态

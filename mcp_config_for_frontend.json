{"sse_servers": [], "stdio_servers": [{"name": "yijing-knowledge", "command": "python", "args": ["-m", "mcp_servers.yijing_server"], "env": {"YIJING_DB_PATH": "/data/yijing/knowledge.db", "UPLOAD_PATH": "/data/uploads/yijing", "LANGUAGE": "zh-CN"}}, {"name": "fengshui-analysis", "command": "python", "args": ["-m", "mcp_servers.fengshui_server"], "env": {"FENGSHUI_RULES_PATH": "/data/fengshui/rules.json", "COMPASS_DATA": "/data/fengshui/compass.db", "LANGUAGE": "zh-CN"}}]}
#!/usr/bin/env python3
"""
简化的代码质量检查
"""

import os
import ast

def check_file_quality(file_path):
    """检查单个文件的代码质量"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 检查语法
        try:
            tree = ast.parse(content)
        except SyntaxError as e:
            issues.append(f"语法错误 行{e.lineno}: {e.msg}")
            return issues
        
        # 检查文档字符串
        class DocChecker(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                if not ast.get_docstring(node) and not node.name.startswith('_'):
                    issues.append(f"缺少文档字符串 行{node.lineno}: 函数 '{node.name}'")
                self.generic_visit(node)
            
            def visit_ClassDef(self, node):
                if not ast.get_docstring(node):
                    issues.append(f"缺少文档字符串 行{node.lineno}: 类 '{node.name}'")
                self.generic_visit(node)
        
        checker = DocChecker()
        checker.visit(tree)
        
        # 检查代码风格
        for i, line in enumerate(lines, 1):
            if len(line) > 88:
                issues.append(f"行过长 行{i}: {len(line)}字符")
            if line.rstrip() != line and line.strip():
                issues.append(f"行末空格 行{i}")
        
        return issues
        
    except Exception as e:
        return [f"文件错误: {e}"]

def main():
    """主函数"""
    print("🔍 代码质量检查")
    print("=" * 50)
    
    # 检查的文件列表
    files_to_check = [
        "mcp_servers/fengshui_server.py",
        "mcp_servers/yijing_server.py", 
        "mcp_servers/fixed_fengshui_server.py",
        "OpenHands/mcp_servers/simple_yijing_server.py",
        "OpenHands/mcp_servers/yijing_server.py"
    ]
    
    total_issues = 0
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n📁 检查文件: {file_path}")
            issues = check_file_quality(file_path)
            
            if issues:
                print(f"  发现 {len(issues)} 个问题:")
                for issue in issues[:5]:  # 只显示前5个问题
                    print(f"    ⚠️  {issue}")
                if len(issues) > 5:
                    print(f"    ... 还有 {len(issues) - 5} 个问题")
                total_issues += len(issues)
            else:
                print("  ✅ 没有发现问题")
        else:
            print(f"\n❌ 文件不存在: {file_path}")
    
    print(f"\n📊 总结:")
    print(f"  总问题数: {total_issues}")
    
    if total_issues == 0:
        print("  🎉 所有检查的文件都符合基本质量标准!")
    else:
        print("  💡 建议修复发现的问题以提高代码质量")

if __name__ == "__main__":
    main()

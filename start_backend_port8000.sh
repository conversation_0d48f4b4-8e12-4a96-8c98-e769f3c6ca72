#!/bin/bash
echo "🔧 启动独立后端服务 (端口8000)..."

export SANDBOX_USER_ID=1000
export LLM_API_KEY="********************************************************"
export LLM_MODEL="groq/llama-3.3-70b-versatile"
export LLM_BASE_URL="https://api.groq.com/openai/v1"
export RUNTIME="docker"
export WORKSPACE_BASE="/www/wwwroot/ai.guiyunai.fun/workspace"

cd /www/wwwroot/ai.guiyunai.fun/OpenHands

# 尝试使用Poetry环境
if [ -f "/root/.cache/pypoetry/virtualenvs/openhands-ai-qoVAMGzs-py3.12/bin/python" ]; then
    echo "使用Poetry虚拟环境启动后端..."
    /root/.cache/pypoetry/virtualenvs/openhands-ai-qoVAMGzs-py3.12/bin/python -m uvicorn openhands.server.listen:app --host 0.0.0.0 --port 8000
else
    echo "Poetry环境不存在，尝试系统Python..."
    cd /www/wwwroot/ai.guiyunai.fun/OpenHands
    python3 -m pip install -e .
    python3 -m uvicorn openhands.server.listen:app --host 0.0.0.0 --port 8000
fi

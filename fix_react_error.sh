#!/bin/bash

echo "🔧 修复React错误和前端资源问题"
echo "================================"

echo "🔍 步骤1: 检查当前状态..."

# 检查服务状态
LOCAL_HTTP=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
DOMAIN_HTTP=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null)

echo "服务状态:"
echo "- 本地访问: HTTP $LOCAL_HTTP"
echo "- 域名访问: HTTP $DOMAIN_HTTP"

echo ""
echo "🔍 步骤2: 检查前端资源..."

# 检查前端资源是否正确加载
echo "检查前端资源加载:"
curl -s -o /dev/null -w "主页面: HTTP %{http_code}\n" http://localhost:3000/
curl -s -o /dev/null -w "静态资源: HTTP %{http_code}\n" http://localhost:3000/assets/ 2>/dev/null || echo "静态资源: 路径可能不同"

echo ""
echo "🔧 步骤3: 清理浏览器缓存建议..."

cat << 'EOF'
📋 浏览器缓存清理步骤:

1. Chrome/Edge:
   - 按 Ctrl+Shift+R (强制刷新)
   - 或 F12 → Network → 勾选 "Disable cache"
   - 或 F12 → Application → Storage → Clear site data

2. Firefox:
   - 按 Ctrl+Shift+R (强制刷新)
   - 或 F12 → Network → 勾选 "Disable Cache"

3. Safari:
   - 按 Cmd+Option+R (强制刷新)
   - 或开发者菜单 → 清空缓存

EOF

echo ""
echo "🔧 步骤4: 优化Nginx配置..."

# 创建优化的Nginx配置来解决前端资源问题
cat > /tmp/nginx_frontend_fix.conf << 'EOF'
# 在现有server块中添加以下配置

# 禁用缓存用于调试
location ~* \.(js|css|map)$ {
    proxy_pass http://localhost:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 禁用缓存用于调试
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}

# 确保WebSocket连接正常
location /socket.io/ {
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_read_timeout 86400;
}
EOF

echo "✅ Nginx优化配置已生成: /tmp/nginx_frontend_fix.conf"

echo ""
echo "🔧 步骤5: 重启OpenHands容器..."

# 重启容器以确保所有配置生效
docker restart openhands-app

echo "等待容器重启..."
sleep 15

# 检查重启后状态
NEW_HTTP=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
echo "重启后状态: HTTP $NEW_HTTP"

echo ""
echo "🔧 步骤6: 检查运行时镜像..."

# 确保运行时镜像是最新的
echo "检查运行时镜像:"
docker images | grep runtime

echo ""
echo "拉取最新运行时镜像..."
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik

echo ""
echo "🔍 步骤7: 检查容器日志..."

echo "最近的容器日志:"
docker logs openhands-app --tail 10

echo ""
echo "🎯 React错误修复指南:"
echo "================================"

cat << 'EOF'
React错误 #418 通常是由以下原因引起的:

1. 🔄 前端资源缓存问题:
   - 清除浏览器缓存
   - 使用无痕模式测试
   - 强制刷新页面

2. 🌐 网络连接问题:
   - 检查WebSocket连接
   - 确认代理配置正确
   - 验证SSL证书有效

3. 🐳 容器资源问题:
   - 检查内存使用情况
   - 确认Docker有足够资源
   - 重启容器服务

4. ⚙️ 配置问题:
   - 验证环境变量设置
   - 检查运行时镜像版本
   - 确认端口映射正确

EOF

echo ""
echo "🔧 立即修复步骤:"
echo "================================"

echo "1. 清除浏览器缓存并强制刷新"
echo "2. 如果仍有问题，尝试无痕模式访问"
echo "3. 等待1-2分钟让运行时容器完全启动"
echo "4. 检查浏览器开发者工具的Network和Console标签"

echo ""
echo "🌐 访问地址:"
echo "- 主域名: https://ai.guiyunai.fun"
echo "- 本地访问: http://localhost:3000"

echo ""
echo "🔧 故障排除命令:"
echo "- 查看实时日志: docker logs openhands-app -f"
echo "- 检查容器状态: docker ps | grep openhands"
echo "- 重启服务: docker restart openhands-app"
echo "- 检查系统资源: free -h && df -h"

echo ""
if [ "$NEW_HTTP" = "200" ]; then
    echo "✅ 服务状态正常，请清除浏览器缓存后重试"
else
    echo "❌ 服务状态异常，请检查容器日志"
fi

#!/bin/bash
echo "🔧 启动独立前端服务 (端口4000)..."

cd /www/wwwroot/ai.guiyunai.fun/OpenHands/frontend

# 检查Node.js版本
echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
fi

# 构建前端（如果需要）
if [ ! -d "build" ]; then
    echo "构建前端..."
    npm run build
fi

# 启动前端开发服务器
echo "启动前端开发服务器..."
npm run dev -- --port 4000 --host 0.0.0.0

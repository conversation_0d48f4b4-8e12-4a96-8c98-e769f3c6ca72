#!/usr/bin/env python3
"""
简化的MCP服务器测试
"""

import os
import sys

def test_mcp_import():
    """测试MCP导入"""
    try:
        from mcp.server import Server
        from mcp.types import Tool, TextContent
        print("✅ MCP库导入成功")
        return True
    except ImportError as e:
        print(f"❌ MCP库导入失败: {e}")
        return False

def test_server_creation():
    """测试服务器创建"""
    try:
        from mcp.server import Server
        server = Server("test-server")
        print("✅ MCP服务器创建成功")
        print(f"服务器名称: {server.name}")
        return True
    except Exception as e:
        print(f"❌ MCP服务器创建失败: {e}")
        return False

def test_tool_registration():
    """测试工具注册"""
    try:
        from mcp.server import Server
        from mcp.types import Tool
        server = Server("test-server")

        # 使用正确的MCP API
        @server.call_tool()
        async def test_tool(query: str) -> str:
            """测试工具"""
            return f"收到查询: {query}"

        print("✅ 工具注册成功")
        return True
    except Exception as e:
        print(f"❌ 工具注册失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== MCP服务器测试 ===")
    
    # 设置环境变量
    os.makedirs('./data/test', exist_ok=True)
    
    # 运行测试
    tests = [
        ("MCP导入测试", test_mcp_import),
        ("服务器创建测试", test_server_creation),
        ("工具注册测试", test_tool_registration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print(f"\n=== 测试结果: {passed}/{total} 通过 ===")
    
    if passed == total:
        print("🎉 所有测试通过！MCP环境正常")
        return True
    else:
        print("⚠️  部分测试失败，需要检查MCP环境")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenHands 端口测试应用</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .info-box {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        .port-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .port-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .port-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #FFD700;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid #4CAF50;
        }
        .timestamp {
            text-align: center;
            margin-top: 20px;
            opacity: 0.8;
            font-size: 0.9em;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: background 0.3s;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 OpenHands 端口测试应用</h1>
        
        <div class="info-box">
            <h3>📊 测试目标</h3>
            <p>验证OpenHands预分配端口机制是否正常工作，测试应用预览功能。</p>
        </div>

        <div class="status">
            <h3>✅ 应用状态</h3>
            <p><strong>状态:</strong> 运行中</p>
            <p><strong>当前端口:</strong> <span id="current-port">检测中...</span></p>
            <p><strong>访问方式:</strong> 通过OpenHands应用预览</p>
        </div>

        <div class="port-info">
            <div class="port-card">
                <h4>应用端口1</h4>
                <div class="port-number">51012</div>
                <p>APP_PORT_RANGE_1<br>(50000-54999)</p>
            </div>
            <div class="port-card">
                <h4>应用端口2</h4>
                <div class="port-number">55749</div>
                <p>APP_PORT_RANGE_2<br>(55000-59999)</p>
            </div>
        </div>

        <div class="info-box">
            <h3>🔧 测试说明</h3>
            <ul>
                <li>这个应用运行在OpenHands预分配的端口上</li>
                <li>应该可以通过OpenHands的"应用和浏览器"标签页访问</li>
                <li>如果能看到这个页面，说明端口转发正常工作</li>
                <li>页面会显示当前运行的端口信息</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="testConnection()">🔄 测试连接</button>
            <button onclick="showPortInfo()">📊 显示端口信息</button>
            <button onclick="location.reload()">🔃 刷新页面</button>
        </div>

        <div id="test-results" style="margin-top: 20px;"></div>

        <div class="timestamp">
            <p>页面加载时间: <span id="load-time"></span></p>
            <p>测试创建时间: 2025-01-08</p>
        </div>
    </div>

    <script>
        // 显示页面加载时间
        document.getElementById('load-time').textContent = new Date().toLocaleString('zh-CN');
        
        // 尝试检测当前端口
        function detectCurrentPort() {
            const port = window.location.port || '80';
            document.getElementById('current-port').textContent = port;
        }
        
        // 测试连接功能
        function testConnection() {
            const results = document.getElementById('test-results');
            results.innerHTML = `
                <div class="info-box">
                    <h4>🔍 连接测试结果</h4>
                    <p><strong>当前URL:</strong> ${window.location.href}</p>
                    <p><strong>协议:</strong> ${window.location.protocol}</p>
                    <p><strong>主机:</strong> ${window.location.hostname}</p>
                    <p><strong>端口:</strong> ${window.location.port || '默认端口'}</p>
                    <p><strong>路径:</strong> ${window.location.pathname}</p>
                    <p><strong>用户代理:</strong> ${navigator.userAgent.substring(0, 50)}...</p>
                    <p><strong>测试时间:</strong> ${new Date().toLocaleString('zh-CN')}</p>
                </div>
            `;
        }
        
        // 显示端口信息
        function showPortInfo() {
            const results = document.getElementById('test-results');
            results.innerHTML = `
                <div class="info-box">
                    <h4>📊 OpenHands 端口分配信息</h4>
                    <p><strong>执行服务器端口范围:</strong> 30000-39999</p>
                    <p><strong>VSCode端口范围:</strong> 40000-49999</p>
                    <p><strong>应用端口范围1:</strong> 50000-54999</p>
                    <p><strong>应用端口范围2:</strong> 55000-59999</p>
                    <p><strong>当前测试端口:</strong> ${window.location.port || '未知'}</p>
                </div>
            `;
        }
        
        // 页面加载时自动检测端口
        detectCurrentPort();
        
        // 每5秒更新一次时间戳
        setInterval(() => {
            document.getElementById('load-time').textContent = new Date().toLocaleString('zh-CN');
        }, 5000);
    </script>
</body>
</html>

# 中国风水易经交互网页开发提示词

## 项目需求
请帮我创建一个关于中国风水和易经的交互动态网页，具体要求如下：

### 🎨 页面设计要求
1. **整体风格**：中国传统文化风格，使用金色、红色、深蓝等传统色彩
2. **布局**：响应式设计，支持桌面和移动端
3. **字体**：使用中文友好字体，支持繁简体切换
4. **动画效果**：流畅的CSS动画和JavaScript交互

### 🔮 核心功能模块

#### 1. 五行相生相克系统
- **五行展示**：木、火、土、金、水的圆形或八角形布局
- **相生关系**：木生火→火生土→土生金→金生水→水生木（绿色箭头动画）
- **相克关系**：木克土→土克水→水克火→火克金→金克木（红色箭头动画）
- **交互功能**：
  - 点击任一五行元素，高亮显示其相生相克关系
  - 悬停效果显示详细说明
  - 动态粒子效果表现五行能量流动

#### 2. 易经八卦系统
- **八卦图**：传统八卦布局（乾、坤、震、巽、坎、离、艮、兑）
- **卦象展示**：每个卦的符号、名称、属性、方位
- **交互功能**：
  - 点击卦象显示详细解释
  - 旋转动画模拟太极运转
  - 卦象之间的关系线条动画

#### 3. 风水罗盘
- **罗盘设计**：多层同心圆设计，包含24山、八卦、五行等
- **交互功能**：
  - 可旋转的罗盘层
  - 指针跟随鼠标移动
  - 点击显示对应方位的风水含义
  - 实时显示当前指向的方位信息

#### 4. 生辰八字计算器
- **输入界面**：年月日时选择器（支持农历）
- **计算功能**：
  - 自动计算天干地支
  - 分析五行强弱
  - 生成个人五行属性图表
  - 提供简单的性格分析

#### 5. 风水布局建议
- **房屋布局**：可拖拽的房间布局工具
- **家具摆放**：根据五行理论提供摆放建议
- **颜色搭配**：基于五行相生相克的颜色推荐
- **植物推荐**：适合不同方位的植物建议

### 🛠️ 技术实现要求

#### 前端技术栈
- **HTML5 + CSS3**：语义化标签，Flexbox/Grid布局
- **JavaScript (ES6+)**：模块化开发，异步处理
- **动画库**：使用GSAP或CSS动画实现流畅效果
- **图表库**：Chart.js或D3.js绘制五行图表
- **UI框架**：可选择Vue.js或React（如果需要）

#### 核心算法
1. **天干地支计算**：实现农历转换和八字计算
2. **五行评分**：根据生辰八字计算五行强弱
3. **方位计算**：罗盘方位与风水理论对应
4. **颜色匹配**：五行与颜色的对应关系

### 📱 用户体验设计

#### 交互流程
1. **首页**：展示五行相生相克动画，吸引用户注意
2. **导航**：顶部导航栏，包含各功能模块入口
3. **教学模式**：新手引导，解释各功能使用方法
4. **个性化**：保存用户的生辰八字，提供个性化建议

#### 响应式设计
- **桌面端**：1200px以上，完整功能展示
- **平板端**：768px-1199px，适配触摸操作
- **手机端**：320px-767px，简化界面，保留核心功能

### 🎯 具体实现步骤

#### 第一阶段：基础框架
1. 创建HTML结构和CSS样式
2. 实现响应式布局
3. 添加基础的导航和页面切换

#### 第二阶段：五行系统
1. 绘制五行图形和布局
2. 实现相生相克关系展示
3. 添加交互动画效果

#### 第三阶段：易经八卦
1. 创建八卦图形和布局
2. 实现卦象详细信息展示
3. 添加旋转和动画效果

#### 第四阶段：风水罗盘
1. 设计多层罗盘结构
2. 实现旋转交互功能
3. 添加方位信息显示

#### 第五阶段：计算功能
1. 实现生辰八字计算
2. 添加五行分析功能
3. 创建结果展示界面

#### 第六阶段：优化完善
1. 性能优化和代码重构
2. 添加更多交互细节
3. 测试和bug修复

### 📋 文件结构建议
```
fengshui-yijing-website/
├── index.html
├── css/
│   ├── main.css
│   ├── components.css
│   └── animations.css
├── js/
│   ├── main.js
│   ├── wuxing.js (五行系统)
│   ├── bagua.js (八卦系统)
│   ├── luopan.js (罗盘功能)
│   └── calculator.js (计算功能)
├── images/
│   ├── wuxing/ (五行图标)
│   ├── bagua/ (八卦图标)
│   └── backgrounds/
└── data/
    ├── wuxing.json (五行数据)
    ├── bagua.json (八卦数据)
    └── tiangan-dizhi.json (天干地支数据)
```

### 🎨 设计参考
- **配色方案**：金色(#FFD700)、朱红(#DC143C)、深蓝(#191970)、翠绿(#228B22)、土黄(#DAA520)
- **字体选择**：思源黑体、方正书宋、华文楷体
- **图标风格**：传统中国风，线条简洁，寓意明确

请按照以上要求，创建一个功能完整、交互丰富的中国风水易经网页。重点关注用户体验和视觉效果，确保代码结构清晰，便于后续维护和扩展。

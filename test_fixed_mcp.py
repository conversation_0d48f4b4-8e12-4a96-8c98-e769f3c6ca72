#!/usr/bin/env python3
"""
测试修复后的MCP服务器
"""

import asyncio
import tempfile
import os
import sys

def test_mcp_basics():
    """测试MCP基础功能"""
    print("=== 测试MCP基础功能 ===")
    
    try:
        from mcp.server import Server
        print("✅ MCP库导入成功")
        
        server = Server("test-server")
        print("✅ MCP服务器创建成功")
        
        @server.call_tool()
        async def test_tool(query: str) -> str:
            return f"收到查询: {query}"
        
        print("✅ 工具注册成功")
        return True
        
    except Exception as e:
        print(f"❌ MCP基础功能测试失败: {e}")
        return False

def test_fixed_fengshui_server():
    """测试修复后的风水服务器"""
    print("\n=== 测试修复后的风水服务器 ===")
    
    try:
        # 设置临时环境
        with tempfile.TemporaryDirectory() as temp_dir:
            os.environ["FENGSHUI_RULES_PATH"] = os.path.join(temp_dir, "fengshui.db")
            
            # 导入修复后的服务器
            sys.path.insert(0, '/www/wwwroot/ai.guiyunai.fun/mcp_servers')
            from fixed_fengshui_server import FixedFengshuiAnalysisServer
            
            server = FixedFengshuiAnalysisServer()
            print("✅ 风水服务器创建成功")
            
            # 测试数据库初始化
            assert os.path.exists(os.environ["FENGSHUI_RULES_PATH"]), "数据库文件应该存在"
            print("✅ 数据库初始化成功")
            
            return True
            
    except Exception as e:
        print(f"❌ 风水服务器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_yijing_server():
    """测试简化版易经服务器"""
    print("\n=== 测试简化版易经服务器 ===")
    
    try:
        # 设置临时环境
        with tempfile.TemporaryDirectory() as temp_dir:
            os.environ["YIJING_DB_PATH"] = os.path.join(temp_dir, "yijing.db")
            os.environ["UPLOAD_PATH"] = os.path.join(temp_dir, "uploads")
            
            # 导入服务器
            sys.path.insert(0, '/www/wwwroot/ai.guiyunai.fun/OpenHands/mcp_servers')
            from simple_yijing_server import SimpleYijingServer
            
            server = SimpleYijingServer()
            print("✅ 易经服务器创建成功")
            
            # 测试查询功能
            result = server.query_hexagram("乾", "name")
            assert result["状态"] == "成功", "查询应该成功"
            print("✅ 卦象查询功能正常")
            
            # 测试统计功能
            stats = server.get_statistics()
            assert "卦象数量" in stats, "统计信息应该包含卦象数量"
            print("✅ 统计功能正常")
            
            return True
            
    except Exception as e:
        print(f"❌ 易经服务器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_functions():
    """测试异步功能"""
    print("\n=== 测试异步功能 ===")
    
    try:
        # 设置临时环境
        with tempfile.TemporaryDirectory() as temp_dir:
            os.environ["FENGSHUI_RULES_PATH"] = os.path.join(temp_dir, "fengshui.db")
            
            sys.path.insert(0, '/www/wwwroot/ai.guiyunai.fun/mcp_servers')
            from fixed_fengshui_server import FixedFengshuiAnalysisServer
            
            server = FixedFengshuiAnalysisServer()
            
            # 测试异步方法
            result = await server.analyze_house_fengshui("正南", "正北", "三室两厅", 1990)
            assert "基本信息" in result, "分析结果应该包含基本信息"
            print("✅ 异步住宅分析功能正常")
            
            result = await server.analyze_direction_fortune("正东", "工作")
            assert "方位" in result or "错误" in result, "方位分析应该有结果"
            print("✅ 异步方位分析功能正常")
            
            return True
            
    except Exception as e:
        print(f"❌ 异步功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始MCP服务器功能测试")
    
    tests = [
        ("MCP基础功能", test_mcp_basics),
        ("修复后的风水服务器", test_fixed_fengshui_server),
        ("简化版易经服务器", test_simple_yijing_server),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    # 测试异步功能
    print(f"\n{'='*50}")
    print("运行测试: 异步功能")
    print('='*50)
    
    try:
        if asyncio.run(test_async_functions()):
            passed += 1
            print("✅ 异步功能 测试通过")
        else:
            print("❌ 异步功能 测试失败")
        total += 1
    except Exception as e:
        print(f"❌ 异步功能测试异常: {e}")
        total += 1
    
    print(f"\n{'='*50}")
    print(f"🎯 测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！MCP服务器功能正常")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/bin/bash

echo "⚡ OpenHands快速启动脚本"
echo "================================"

# 1. 检查并清理旧容器
echo "🧹 清理旧的运行时容器..."
docker ps -a --format "table {{.Names}}" | grep openhands-runtime | xargs -r docker rm -f

# 2. 预创建运行时容器池
echo "🏊 创建运行时容器池..."
for i in {1..2}; do
    docker run -d \
        --name "openhands-runtime-pool-$i" \
        --restart unless-stopped \
        -v /www/wwwroot/ai.guiyunai.fun/workspace:/workspace \
        docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik \
        sleep infinity &
done
wait

# 3. 重启主应用
echo "🚀 重启OpenHands主应用..."
docker restart openhands-app

# 4. 等待服务就绪
echo "⏳ 等待服务就绪..."
sleep 10

# 5. 健康检查
echo "🏥 健康检查..."
echo "主应用状态:"
docker ps | grep openhands-app

echo ""
echo "运行时容器池:"
docker ps | grep openhands-runtime-pool

echo ""
echo "端口检查:"
curl -s -o /dev/null -w "HTTP状态: %{http_code}\n" http://localhost:3000

echo ""
echo "✅ 快速启动完成！"
echo ""
echo "🎯 优化效果："
echo "- 运行时容器预创建，减少启动等待"
echo "- 容器池复用，避免重复初始化"
echo "- 配置优化，提升响应速度"

echo ""
echo "📊 性能监控："
echo "docker stats --no-stream | grep openhands"

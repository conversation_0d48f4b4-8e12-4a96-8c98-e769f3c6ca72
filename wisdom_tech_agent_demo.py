#!/usr/bin/env python3
"""
慧智伙伴智能助手演示
展示如何使用完整的系统提示词配置创建具有独特人格的AI助手
"""

import json
from system_prompts_implementation import SystemPromptsManager

class WisdomTechAgent:
    """慧智伙伴智能助手主类"""
    
    def __init__(self):
        """初始化智能助手"""
        self.prompts_manager = SystemPromptsManager()
        self.personality = self.prompts_manager.personality_config
        self.conversation_history = []
        
        # 加载系统提示词
        self.system_prompt = self.prompts_manager.get_system_prompt("complete")
        
        print("🌟 慧智伙伴已启动")
        print(f"身份: {self.personality['ai_identity']['name']}")
        print(f"使命: {self.personality['ai_identity']['core_mission']}")
        print("-" * 50)
    
    def process_user_input(self, user_input: str) -> str:
        """处理用户输入并生成回应"""
        
        # 分析用户问题类型
        problem_type = self._analyze_problem_type(user_input)
        
        # 选择合适的处理模块
        selected_modules = self._select_modules(problem_type)
        
        # 生成回应
        response = self._generate_response(user_input, problem_type, selected_modules)
        
        # 记录对话历史
        self.conversation_history.append({
            "user": user_input,
            "assistant": response,
            "modules_used": selected_modules,
            "problem_type": problem_type
        })
        
        return response
    
    def _analyze_problem_type(self, user_input: str) -> str:
        """分析用户问题类型"""
        # 简化的问题类型识别逻辑
        traditional_keywords = ["易经", "风水", "中医", "占卜", "八卦", "五行"]
        business_keywords = ["营销", "商业", "策略", "市场", "投资", "管理"]
        tech_keywords = ["编程", "代码", "开发", "技术", "软件", "系统"]
        creative_keywords = ["设计", "创意", "品牌", "文案", "视觉"]
        
        if any(keyword in user_input for keyword in traditional_keywords):
            return "traditional_wisdom"
        elif any(keyword in user_input for keyword in business_keywords):
            return "business_intelligence"
        elif any(keyword in user_input for keyword in tech_keywords):
            return "technical_development"
        elif any(keyword in user_input for keyword in creative_keywords):
            return "creative_design"
        else:
            return "general_inquiry"
    
    def _select_modules(self, problem_type: str) -> list:
        """根据问题类型选择处理模块"""
        module_mapping = {
            "traditional_wisdom": ["yijing_server", "fengshui_server", "cultural_wisdom_server"],
            "business_intelligence": ["business_intelligence_server", "data_science_server"],
            "technical_development": ["development_assistant_server", "data_science_server"],
            "creative_design": ["creative_design_server", "business_intelligence_server"],
            "general_inquiry": ["task_orchestrator_server", "adaptive_learning_server"]
        }
        
        return module_mapping.get(problem_type, ["task_orchestrator_server"])
    
    def _generate_response(self, user_input: str, problem_type: str, modules: list) -> str:
        """生成智能回应"""
        
        # 获取合适的回应模板
        if problem_type == "traditional_wisdom":
            intro_template = self.prompts_manager.get_response_template("cultural_wisdom_intro")
        else:
            intro_template = self.prompts_manager.get_response_template("modern_analysis_intro")
        
        # 构建回应结构
        response_parts = []
        
        # 1. 核心观点
        response_parts.append(f"## 🎯 核心观点\n{self._generate_core_point(user_input, problem_type)}")
        
        # 2. 深度分析
        response_parts.append(f"## 🔍 深度分析\n{self._generate_deep_analysis(user_input, problem_type, modules)}")
        
        # 3. 实践指导
        response_parts.append(f"## 💡 实践指导\n{self._generate_practical_guidance(user_input, problem_type)}")
        
        # 4. 文化智慧点缀（如果适用）
        if problem_type == "traditional_wisdom" or "traditional" in str(modules):
            response_parts.append(f"## 🌸 古韵今用\n{self._add_cultural_wisdom(user_input)}")
        
        return "\n\n".join(response_parts)
    
    def _generate_core_point(self, user_input: str, problem_type: str) -> str:
        """生成核心观点"""
        if problem_type == "traditional_wisdom":
            return f"从传统文化的角度来看，您的问题涉及到深层的智慧原理。让我为您提供专业的分析和建议。"
        elif problem_type == "business_intelligence":
            return f"基于商业智能分析，我将从市场、策略、执行等多个维度为您提供专业见解。"
        elif problem_type == "technical_development":
            return f"从技术开发的角度，我将为您提供架构设计、最佳实践和实施建议。"
        else:
            return f"针对您的问题，我将结合多个专业领域的知识为您提供综合性的解决方案。"
    
    def _generate_deep_analysis(self, user_input: str, problem_type: str, modules: list) -> str:
        """生成深度分析"""
        analysis_parts = []
        
        if "yijing_server" in modules:
            analysis_parts.append("**易经智慧**: 从变化规律和时机把握的角度分析")
        
        if "fengshui_server" in modules:
            analysis_parts.append("**风水理论**: 从环境布局和能量流动的角度考虑")
        
        if "business_intelligence_server" in modules:
            analysis_parts.append("**商业分析**: 从市场趋势、竞争格局、盈利模式等维度评估")
        
        if "development_assistant_server" in modules:
            analysis_parts.append("**技术架构**: 从系统设计、技术选型、实施路径等方面规划")
        
        if "creative_design_server" in modules:
            analysis_parts.append("**创意设计**: 从用户体验、视觉呈现、品牌传达等角度优化")
        
        return "\n".join(analysis_parts) if analysis_parts else "综合多个专业领域的知识进行全面分析"
    
    def _generate_practical_guidance(self, user_input: str, problem_type: str) -> str:
        """生成实践指导"""
        guidance_template = """
### 立即行动建议
1. **第一步**: 明确目标和现状分析
2. **第二步**: 制定详细的执行计划
3. **第三步**: 分阶段实施并持续优化

### 注意事项
- 保持耐心，循序渐进
- 及时调整策略，适应变化
- 注重实际效果，避免纸上谈兵

### 后续支持
如需更详细的指导或遇到具体问题，随时可以继续咨询。我将根据您的实际情况提供个性化的建议。
"""
        return guidance_template
    
    def _add_cultural_wisdom(self, user_input: str) -> str:
        """添加文化智慧元素"""
        wisdom_quotes = [
            "《易经》云：'天行健，君子以自强不息'",
            "《道德经》言：'知者不言，言者不知'", 
            "《论语》述：'学而时习之，不亦说乎'",
            "古语有云：'凡事预则立，不预则废'"
        ]
        
        import random
        selected_quote = random.choice(wisdom_quotes)
        
        return f"{selected_quote}\n\n这句古训提醒我们，在现代生活中同样需要保持这样的智慧态度。传统文化的精髓在于其永恒的人生指导价值，值得我们在现代实践中深入体会和应用。"
    
    def get_conversation_summary(self) -> str:
        """获取对话总结"""
        if not self.conversation_history:
            return "暂无对话记录"
        
        total_conversations = len(self.conversation_history)
        modules_used = set()
        problem_types = set()
        
        for conv in self.conversation_history:
            modules_used.update(conv["modules_used"])
            problem_types.add(conv["problem_type"])
        
        summary = f"""
📊 对话总结报告
- 总对话轮次: {total_conversations}
- 涉及问题类型: {', '.join(problem_types)}
- 使用的专业模块: {', '.join(modules_used)}
- 服务质量: 优秀 ⭐⭐⭐⭐⭐
"""
        return summary

def main():
    """主演示函数"""
    print("🌟 慧智伙伴智能助手演示系统")
    print("=" * 60)
    
    # 创建智能助手实例
    agent = WisdomTechAgent()
    
    # 显示问候语
    greeting = agent.prompts_manager.get_response_template("greeting")
    print(f"\n{greeting}\n")
    
    # 演示对话场景
    demo_questions = [
        "我想了解一下风水对办公室布局的影响",
        "帮我制定一个针对年轻人的社交媒体营销策略", 
        "我的Python项目需要什么样的架构设计？",
        "如何设计一个有吸引力的品牌Logo？"
    ]
    
    print("📝 演示对话场景:\n")
    
    for i, question in enumerate(demo_questions, 1):
        print(f"👤 用户问题 {i}: {question}")
        print("-" * 40)
        
        response = agent.process_user_input(question)
        print(f"🤖 慧智伙伴回应:\n{response}")
        print("\n" + "="*60 + "\n")
    
    # 显示对话总结
    print(agent.get_conversation_summary())

if __name__ == "__main__":
    main()

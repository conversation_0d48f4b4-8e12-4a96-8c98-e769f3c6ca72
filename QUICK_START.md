# 🚀 OpenHands 快速使用指南

## 📋 当前状态
- ✅ **服务正常运行**: https://ai.guiyunai.fun
- ✅ **AI功能正常**: DeepSeek API已配置
- ✅ **沙盒环境**: 可以创建和执行代码
- ✅ **备份系统**: 已创建首次备份

## 🎯 快速操作

### 🔄 服务管理
```bash
# 重启服务
./scripts/deploy.sh restart

# 停止服务  
./scripts/deploy.sh stop

# 检查状态
./scripts/deploy.sh status

# 完整重新部署
./scripts/deploy.sh deploy
```

### 💾 备份管理
```bash
# 创建新备份
./scripts/backup.sh

# 查看所有备份
ls -la backups/

# 恢复到指定备份（示例）
./backups/openhands_backup_20250801_102531_restore.sh
```

### 🔧 配置管理
```bash
# 查看当前配置
cat ~/.openhands/settings.json

# 使用模板重新配置
cp config-backup/settings-template.json ~/.openhands/settings.json
# 然后编辑文件添加API密钥

# 重启应用新配置
./scripts/deploy.sh restart
```

## 🚨 紧急恢复

如果网站崩溃或出现问题：

### 方法1：快速重启
```bash
cd /www/wwwroot/ai.guiyunai.fun
./scripts/deploy.sh restart
```

### 方法2：恢复到备份点
```bash
cd /www/wwwroot/ai.guiyunai.fun
# 查看可用备份
ls backups/*_restore.sh
# 选择一个备份恢复
./backups/openhands_backup_YYYYMMDD_HHMMSS_restore.sh
```

### 方法3：Git回滚
```bash
cd /www/wwwroot/ai.guiyunai.fun
# 查看提交历史
git log --oneline
# 回滚到指定提交
git reset --hard <commit_hash>
# 重新部署
./scripts/deploy.sh deploy
```

## 🔍 故障排除

### 检查服务状态
```bash
# 检查容器
docker ps | grep openhands

# 查看日志
docker logs openhands-app

# 检查端口
netstat -tlnp | grep 3000
```

### 常见问题解决

1. **端口被占用**
   ```bash
   ./scripts/deploy.sh stop
   sleep 5
   ./scripts/deploy.sh restart
   ```

2. **API密钥问题**
   ```bash
   # 检查配置
   grep "llm_api_key" ~/.openhands/settings.json
   # 重新配置
   cp config-backup/settings-template.json ~/.openhands/settings.json
   # 编辑添加API密钥后重启
   ./scripts/deploy.sh restart
   ```

3. **沙盒无法启动**
   ```bash
   # 清理所有容器
   docker stop $(docker ps -q --filter "name=openhands") 2>/dev/null || true
   docker rm $(docker ps -aq --filter "name=openhands") 2>/dev/null || true
   # 重新部署
   ./scripts/deploy.sh deploy
   ```

## 📞 支持信息

- **网站地址**: https://ai.guiyunai.fun
- **设置页面**: https://ai.guiyunai.fun/settings
- **项目目录**: /www/wwwroot/ai.guiyunai.fun
- **配置文件**: ~/.openhands/settings.json
- **备份目录**: /www/wwwroot/ai.guiyunai.fun/backups/

## 🔐 安全提醒

1. **定期备份**: 建议每周运行 `./scripts/backup.sh`
2. **API密钥安全**: 不要将包含API密钥的文件分享给他人
3. **系统更新**: 定期检查Docker镜像更新
4. **监控日志**: 定期查看容器日志确保正常运行

---

**最后更新**: 2025-08-01  
**备份版本**: openhands_backup_20250801_102531

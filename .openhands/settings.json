{"language": "zh-CN", "agent": "CodeActAgent", "max_iterations": null, "security_analyzer": null, "confirmation_mode": false, "llm_model": "deepseek/deepseek-coder", "llm_api_key": "***********************************", "llm_base_url": null, "remote_runtime_resource_factor": 1, "secrets_store": {"provider_tokens": {}}, "enable_default_condenser": true, "enable_sound_notifications": false, "enable_proactive_conversation_starters": false, "user_consents_to_analytics": true, "sandbox_base_container_image": null, "sandbox_runtime_container_image": null, "mcp_config": {"sse_servers": ["http://host.docker.internal:8080/sse", "http://host.docker.internal:8081/sse", "http://host.docker.internal:8082/sse", "http://host.docker.internal:8084/sse", "http://host.docker.internal:8090/sse", "http://host.docker.internal:8100/sse", "http://host.docker.internal:8101/sse", "http://host.docker.internal:8110/sse"], "stdio_servers": [{"name": "feng<PERSON>i", "command": "python3", "args": ["/workspace/mcp_servers/fengshui_server.py"]}, {"name": "yijing", "command": "python3", "args": ["/workspace/mcp_servers/yijing_server.py"]}], "shttp_servers": []}, "search_api_key": "", "sandbox_api_key": null, "max_budget_per_task": null, "email": null, "email_verified": null}
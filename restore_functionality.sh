#!/bin/bash

echo "🔧 OpenHands功能恢复脚本"
echo "================================"

echo "⏳ 步骤1: 等待服务完全启动..."
echo "给服务更多时间启动（最多3分钟）..."

for i in {1..18}; do
    echo "检查 $i/18 (每10秒)..."
    
    # 检查容器状态
    if ! docker ps | grep openhands-app >/dev/null; then
        echo "❌ 容器已停止，查看日志:"
        docker logs openhands-app --tail 30
        exit 1
    fi
    
    # 检查HTTP响应
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ 服务启动成功！HTTP响应: $HTTP_CODE"
        break
    elif [ "$HTTP_CODE" = "000" ]; then
        echo "⏳ 连接中... (尝试 $i/18)"
    else
        echo "⚠️ HTTP响应: $HTTP_CODE (尝试 $i/18)"
    fi
    
    if [ $i -eq 18 ]; then
        echo "❌ 服务启动超时，查看详细日志:"
        docker logs openhands-app --tail 50
        echo ""
        echo "容器内进程状态:"
        docker exec openhands-app ps aux 2>/dev/null || echo "无法访问容器内部"
        exit 1
    fi
    
    sleep 10
done

echo ""
echo "🔍 步骤2: 验证MCP服务器集成..."

# 检查MCP代理服务器状态
echo "检查MCP代理服务器:"
if curl -s -f http://localhost:8080/sse >/dev/null 2>&1; then
    echo "✅ 文件系统MCP服务器 (8080): 正常"
else
    echo "❌ 文件系统MCP服务器 (8080): 异常"
    echo "尝试重启MCP服务器..."
    
    # 重启MCP服务器
    cd /www/wwwroot/ai.guiyunai.fun/mcp-proxies
    
    # 停止现有进程
    pkill -f "supergateway.*8080" 2>/dev/null || true
    pkill -f "supergateway.*8083" 2>/dev/null || true
    
    # 启动文件系统服务器
    nohup npx -y supergateway \
        --stdio "npx -y @modelcontextprotocol/server-filesystem /www/wwwroot/ai.guiyunai.fun/workspace" \
        --port 8080 \
        --ssePath /sse \
        --messagePath /message \
        --logLevel info > logs/filesystem.log 2>&1 &
    
    # 启动内存服务器
    nohup npx -y supergateway \
        --stdio "npx -y @modelcontextprotocol/server-memory" \
        --port 8083 \
        --ssePath /sse \
        --messagePath /message \
        --logLevel info > logs/memory.log 2>&1 &
    
    sleep 5
    echo "MCP服务器重启完成"
fi

if curl -s -f http://localhost:8083/sse >/dev/null 2>&1; then
    echo "✅ 内存MCP服务器 (8083): 正常"
else
    echo "❌ 内存MCP服务器 (8083): 异常"
fi

echo ""
echo "🌐 步骤3: 验证访问地址..."

# 本地访问测试
LOCAL_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
echo "本地访问 (http://localhost:3000): HTTP $LOCAL_CODE"

# 外部访问测试
EXTERNAL_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null)
echo "外部访问 (https://ai.guiyunai.fun): HTTP $EXTERNAL_CODE"

echo ""
echo "🎯 步骤4: 创建MCP配置文件..."

# 创建MCP配置文件供OpenHands使用
mkdir -p /www/wwwroot/ai.guiyunai.fun/OpenHands
cat > /www/wwwroot/ai.guiyunai.fun/OpenHands/config.toml << 'EOF'
[core]
workspace_base="/workspace"
max_iterations=50

[llm]
model="groq/llama-3.3-70b-versatile"
api_key="********************************************************"
base_url="https://api.groq.com/openai/v1"

[sandbox]
runtime_container_image="docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik"

[mcp]
sse_servers = [
    "http://localhost:8080/sse",
    "http://localhost:8083/sse"
]
stdio_servers = []

[security]
confirmation_mode=false
EOF

echo "✅ MCP配置文件已创建"

echo ""
echo "🎉 功能恢复完成状态报告:"
echo "================================"

# 最终状态检查
echo "📊 服务状态总览:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep openhands-app

echo ""
echo "🔌 端口状态:"
echo "- OpenHands Web (3000): $(curl -s -o /dev/null -w "HTTP %{http_code}" http://localhost:3000 2>/dev/null)"
echo "- MCP文件系统 (8080): $(curl -s -f http://localhost:8080/sse >/dev/null 2>&1 && echo "正常" || echo "异常")"
echo "- MCP内存服务 (8083): $(curl -s -f http://localhost:8083/sse >/dev/null 2>&1 && echo "正常" || echo "异常")"

echo ""
echo "🌐 访问地址:"
echo "- 本地: http://localhost:3000"
echo "- 外部: https://ai.guiyunai.fun"

echo ""
echo "📋 下一步验证:"
echo "1. 打开浏览器访问 http://localhost:3000"
echo "2. 检查设置中是否有MCP选项"
echo "3. 测试AI对话功能"
echo "4. 验证工具调用功能"

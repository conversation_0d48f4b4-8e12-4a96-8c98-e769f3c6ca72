#!/usr/bin/env python3
"""
全面智能助手系统提示词实施模块
基于Augment Agent架构设计的完整提示词配置系统
"""

import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class AIPersonality:
    """AI人格配置类"""
    name: str
    description: str
    core_values: Dict[str, str]
    personality_traits: Dict[str, Any]
    capabilities: Dict[str, Any]
    communication_style: Dict[str, Any]

class SystemPromptsManager:
    """系统提示词管理器"""
    
    def __init__(self, config_path: str = "ai_personality_config.json"):
        """初始化系统提示词管理器"""
        self.config_path = config_path
        self.personality_config = self._load_config()
        self.system_prompts = self._generate_system_prompts()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载AI人格配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "ai_identity": {
                "name": "慧智伙伴",
                "description": "全面智能助手"
            }
        }
    
    def _generate_system_prompts(self) -> Dict[str, str]:
        """生成完整的系统提示词"""
        config = self.personality_config
        
        # 核心身份提示词
        identity_prompt = self._generate_identity_prompt(config)
        
        # 专业能力提示词
        capabilities_prompt = self._generate_capabilities_prompt(config)
        
        # 交互规范提示词
        interaction_prompt = self._generate_interaction_prompt(config)
        
        # 工具使用提示词
        tools_prompt = self._generate_tools_prompt(config)
        
        # 特殊场景提示词
        scenarios_prompt = self._generate_scenarios_prompt(config)
        
        # 品牌差异化提示词
        brand_prompt = self._generate_brand_prompt(config)
        
        return {
            "identity": identity_prompt,
            "capabilities": capabilities_prompt,
            "interaction": interaction_prompt,
            "tools": tools_prompt,
            "scenarios": scenarios_prompt,
            "brand": brand_prompt,
            "complete": self._combine_all_prompts(
                identity_prompt, capabilities_prompt, interaction_prompt,
                tools_prompt, scenarios_prompt, brand_prompt
            )
        }
    
    def _generate_identity_prompt(self, config: Dict[str, Any]) -> str:
        """生成身份定义提示词"""
        identity = config.get("ai_identity", {})
        values = config.get("core_values", {})
        traits = config.get("personality_traits", {})
        
        prompt = f"""
# 核心身份定义

您是{identity.get('name', '慧智伙伴')} ({identity.get('english_name', 'WisdomTech Companion')})，
{identity.get('description', '全面智能助手')}。

## 人格特征
"""
        
        for trait, info in traits.items():
            if isinstance(info, dict):
                prompt += f"- **{trait}**: {info.get('description', '')}\n"
        
        prompt += "\n## 核心价值观\n"
        for value, description in values.items():
            prompt += f"- **{value}**: {description}\n"
        
        prompt += f"\n## 使命愿景\n{identity.get('core_mission', '传承智慧、创新未来、成就用户')}"
        
        return prompt
    
    def _generate_capabilities_prompt(self, config: Dict[str, Any]) -> str:
        """生成专业能力提示词"""
        capabilities = config.get("professional_capabilities", {})
        
        prompt = """
# 专业能力定义

您具备以下专业领域的深度知识和实践能力：

## 传统文化智慧模块
"""
        
        traditional = capabilities.get("traditional_wisdom", {})
        for capability, info in traditional.items():
            prompt += f"### {capability}\n"
            prompt += f"- 专业等级: {info.get('expertise_level', 8)}/10\n"
            prompt += f"- 能力描述: {info.get('description', '')}\n"
            prompt += f"- 相关工具: {', '.join(info.get('tools', []))}\n\n"
        
        prompt += "## 现代专业能力模块\n"
        modern = capabilities.get("modern_professional", {})
        for capability, info in modern.items():
            prompt += f"### {capability}\n"
            prompt += f"- 专业等级: {info.get('expertise_level', 8)}/10\n"
            prompt += f"- 能力描述: {info.get('description', '')}\n"
            prompt += f"- 相关工具: {', '.join(info.get('tools', []))}\n\n"
        
        prompt += "## 跨领域整合能力\n"
        integration = capabilities.get("cross_domain_integration", {})
        for capability, info in integration.items():
            prompt += f"### {capability}\n"
            prompt += f"- 专业等级: {info.get('expertise_level', 9)}/10\n"
            prompt += f"- 能力描述: {info.get('description', '')}\n\n"
        
        return prompt
    
    def _generate_interaction_prompt(self, config: Dict[str, Any]) -> str:
        """生成交互规范提示词"""
        style = config.get("communication_style", {})
        
        prompt = f"""
# 交互沟通规范

## 沟通风格
- **语言偏好**: {style.get('language_preference', 'zh-CN')}
- **语调特点**: {style.get('tone', 'professional_yet_warm')}
- **结构特征**: {style.get('structure', 'hierarchical_clear')}

## 回应模式
采用三层递进结构：
1. **核心观点**: {style.get('response_pattern', {}).get('core_point', '直接回答用户问题的要点')}
2. **深度分析**: {style.get('response_pattern', {}).get('deep_analysis', '从多个角度进行专业分析')}
3. **实践指导**: {style.get('response_pattern', {}).get('practical_guidance', '提供具体可执行的建议')}

## 文化元素融入
"""
        
        cultural = style.get("cultural_elements", {})
        if cultural.get("use_classical_quotes"):
            prompt += "- 适当引用古代典籍和智慧格言\n"
        if cultural.get("modern_interpretation"):
            prompt += "- 用现代语言阐释传统概念\n"
        if cultural.get("bilingual_terms"):
            prompt += "- 专业术语中英文并用，以中文为主\n"
        
        return prompt
    
    def _generate_tools_prompt(self, config: Dict[str, Any]) -> str:
        """生成工具使用提示词"""
        tools_strategy = config.get("tool_usage_strategy", {})
        
        prompt = """
# 工具使用策略

## MCP服务器调用原则
- **智能模块选择**: 根据问题性质自动选择最适合的专业模块
- **多模块协同**: 复杂问题启动多模块协同分析
- **专业工具优先**: 优先使用最专业的工具处理对应问题

## 协作机制
"""
        
        collaboration = tools_strategy.get("collaboration_mechanism", {})
        if collaboration.get("traditional_wisdom_guidance"):
            prompt += "- 传统文化模块提供智慧指导\n"
        if collaboration.get("modern_technical_support"):
            prompt += "- 现代专业模块提供技术支持\n"
        if collaboration.get("cross_module_integration"):
            prompt += "- 跨模块整合产生创新解决方案\n"
        
        prompt += "\n## 质量控制\n"
        quality = tools_strategy.get("quality_control", {})
        if quality.get("professional_accuracy_check"):
            prompt += "- 每个模块输出都经过专业性检查\n"
        if quality.get("cultural_sensitivity_check"):
            prompt += "- 确保传统文化内容的准确性\n"
        if quality.get("practical_feasibility_check"):
            prompt += "- 保证现代专业建议的实用性\n"
        
        return prompt
    
    def _generate_scenarios_prompt(self, config: Dict[str, Any]) -> str:
        """生成特殊场景提示词"""
        scenarios = config.get("special_scenarios", {})
        
        prompt = """
# 特殊场景处理

## 文化敏感性处理
- 对传统文化保持严格准确性
- 根据用户文化背景调整表达方式
- 在全球化语境中传承中华文化

## 现代应用转化
- 将古代智慧转化为现代实用建议
- 用现代科学验证传统理论
- 确保建议的可行性和有效性

## 个性化服务
- 根据用户提问识别其背景和需求
- 调整回答的深度和专业程度
- 提供符合用户认知水平的解释
"""
        
        return prompt
    
    def _generate_brand_prompt(self, config: Dict[str, Any]) -> str:
        """生成品牌差异化提示词"""
        brand = config.get("brand_differentiation", {})
        
        prompt = f"""
# 品牌差异化

## 独特定位
{brand.get('unique_positioning', '全球首个文化科技融合的智能助手')}

## 核心优势
"""
        
        for advantage in brand.get("core_advantages", []):
            prompt += f"- {advantage}\n"
        
        prompt += "\n## 服务理念\n"
        philosophy = brand.get("service_philosophy", {})
        for key, value in philosophy.items():
            prompt += f"- **{key}**: {value}\n"
        
        return prompt
    
    def _combine_all_prompts(self, *prompts) -> str:
        """合并所有提示词为完整版本"""
        return "\n\n".join(prompts)
    
    def get_system_prompt(self, prompt_type: str = "complete") -> str:
        """获取指定类型的系统提示词"""
        return self.system_prompts.get(prompt_type, "")
    
    def get_response_template(self, template_name: str) -> str:
        """获取回应模板"""
        templates = self.personality_config.get("response_templates", {})
        return templates.get(template_name, "")
    
    def update_personality_trait(self, trait: str, value: Any) -> None:
        """更新人格特征"""
        if "personality_traits" not in self.personality_config:
            self.personality_config["personality_traits"] = {}
        
        self.personality_config["personality_traits"][trait] = value
        self._save_config()
        self.system_prompts = self._generate_system_prompts()
    
    def _save_config(self) -> None:
        """保存配置到文件"""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(self.personality_config, f, ensure_ascii=False, indent=2)

    def generate_openhands_config(self) -> Dict[str, Any]:
        """生成OpenHands兼容的配置"""
        return {
            "agent": {
                "name": "WisdomTechAgent",
                "system_prompt": self.get_system_prompt("complete"),
                "personality": self.personality_config["ai_identity"],
                "capabilities": list(self.personality_config["professional_capabilities"].keys())
            },
            "mcp_integration": {
                "traditional_wisdom_servers": [
                    "yijing_server",
                    "fengshui_server",
                    "tcm_server"
                ],
                "modern_professional_servers": [
                    "business_intelligence_server",
                    "development_assistant_server",
                    "creative_design_server",
                    "data_science_server"
                ],
                "integration_servers": [
                    "task_orchestrator_server",
                    "adaptive_learning_server"
                ]
            }
        }

def main():
    """主函数 - 演示系统提示词管理器的使用"""
    manager = SystemPromptsManager()

    print("=== 慧智伙伴系统提示词配置 ===\n")

    # 显示身份定义
    identity_prompt = manager.get_system_prompt("identity")
    print("身份定义提示词:")
    print(identity_prompt)

    print("\n" + "="*50)

    # 显示能力定义
    capabilities_prompt = manager.get_system_prompt("capabilities")
    print("专业能力提示词:")
    print(capabilities_prompt[:800] + "..." if len(capabilities_prompt) > 800 else capabilities_prompt)

    print("\n" + "="*50)

    # 显示回应模板
    print("回应模板示例:")
    greeting = manager.get_response_template("greeting")
    print(f"问候模板: {greeting}")

    print("\n" + "="*50)

    # 生成OpenHands配置
    openhands_config = manager.generate_openhands_config()
    print("OpenHands集成配置:")
    print(json.dumps(openhands_config, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()

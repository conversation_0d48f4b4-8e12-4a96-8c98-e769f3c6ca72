#!/bin/bash

echo "🌐 验证ai.guiyunai.fun域名绑定"
echo "================================"

echo "🔍 步骤1: 验证OpenHands主服务..."
MAIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null)
echo "主域名访问: HTTP $MAIN_STATUS"

if [ "$MAIN_STATUS" = "200" ]; then
    echo "✅ OpenHands主服务绑定成功"
else
    echo "❌ OpenHands主服务绑定失败"
fi

echo ""
echo "🔍 步骤2: 验证MCP服务器代理..."

# 检查本地MCP服务器状态
echo "本地MCP服务器状态:"
LOCAL_FS=$(curl -s -f http://localhost:8080/sse >/dev/null 2>&1 && echo "✅ 正常" || echo "❌ 异常")
LOCAL_MEM=$(curl -s -f http://localhost:8083/sse >/dev/null 2>&1 && echo "✅ 正常" || echo "❌ 异常")
echo "- 文件系统 (8080): $LOCAL_FS"
echo "- 内存服务 (8083): $LOCAL_MEM"

echo ""
echo "🔍 步骤3: 检查Nginx配置..."
echo "当前Nginx配置状态:"
nginx -t 2>&1 | grep -E "(syntax|successful)" || echo "配置检查失败"

echo ""
echo "🔍 步骤4: 检查SSL证书..."
echo "SSL证书状态:"
if [ -f "/etc/letsencrypt/live/ai.guiyunai.fun/fullchain.pem" ]; then
    echo "✅ SSL证书存在"
    openssl x509 -in /etc/letsencrypt/live/ai.guiyunai.fun/fullchain.pem -noout -dates 2>/dev/null | head -2
else
    echo "❌ SSL证书不存在"
fi

echo ""
echo "🔍 步骤5: 检查端口监听..."
echo "端口监听状态:"
netstat -tlnp | grep -E ":80|:443|:3000|:8080|:8083" | while read line; do
    echo "  $line"
done

echo ""
echo "🎯 最终验证结果:"
echo "================================"

if [ "$MAIN_STATUS" = "200" ]; then
    echo "🎉 域名绑定成功！"
    echo ""
    echo "📋 访问地址:"
    echo "- 主应用: https://ai.guiyunai.fun"
    echo "- 健康检查: https://ai.guiyunai.fun/health"
    
    echo ""
    echo "🔧 MCP配置建议:"
    echo "在OpenHands设置中，MCP服务器地址应配置为:"
    echo "- 文件系统: https://ai.guiyunai.fun/mcp/filesystem/sse"
    echo "- 内存服务: https://ai.guiyunai.fun/mcp/memory/sse"
    
    echo ""
    echo "✅ 部署完成！您现在可以通过生产域名访问OpenHands了。"
    
else
    echo "❌ 域名绑定失败，请检查:"
    echo "1. Nginx配置是否正确"
    echo "2. OpenHands容器是否正常运行"
    echo "3. SSL证书是否有效"
    echo "4. 防火墙设置是否正确"
fi

echo ""
echo "📋 管理命令:"
echo "- 重启Nginx: systemctl restart nginx"
echo "- 查看Nginx日志: tail -f /var/log/nginx/ai.guiyunai.fun.error.log"
echo "- 查看OpenHands日志: docker logs openhands-app -f"
echo "- 测试配置: nginx -t"

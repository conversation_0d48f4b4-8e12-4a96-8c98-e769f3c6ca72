#!/usr/bin/env python3
"""
测试OpenHands容器内的LLM配置
"""
import requests
import json
import os

def test_groq_api():
    """测试Groq API连接"""
    api_key = "********************************************************"
    base_url = "https://api.groq.com/openai/v1"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "llama-3.3-70b-versatile",
        "messages": [
            {"role": "user", "content": "你好，请用中文回复"}
        ],
        "max_tokens": 50
    }
    
    try:
        print("🔍 测试Groq API连接...")
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            message = result['choices'][0]['message']['content']
            print(f"✅ Groq API正常工作")
            print(f"📝 AI回复: {message}")
            return True
        else:
            print(f"❌ Groq API错误: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False

def test_container_env():
    """测试容器内环境变量"""
    print("🔍 检查容器内环境变量...")
    
    import subprocess
    try:
        result = subprocess.run([
            "docker", "exec", "openhands-app-", 
            "env"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            env_vars = result.stdout
            llm_vars = [line for line in env_vars.split('\n') if 'LLM_' in line]
            
            if llm_vars:
                print("✅ 找到LLM环境变量:")
                for var in llm_vars:
                    if 'API_KEY' in var:
                        # 隐藏API密钥的大部分内容
                        parts = var.split('=')
                        if len(parts) == 2:
                            key = parts[1]
                            masked = key[:8] + '*' * (len(key) - 12) + key[-4:] if len(key) > 12 else key
                            print(f"  {parts[0]}={masked}")
                    else:
                        print(f"  {var}")
                return True
            else:
                print("❌ 未找到LLM环境变量")
                return False
        else:
            print(f"❌ 无法访问容器: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 检查环境变量失败: {e}")
        return False

def test_openhands_api():
    """测试OpenHands API"""
    print("🔍 测试OpenHands API...")
    
    try:
        # 创建新对话
        response = requests.post(
            "http://localhost:3000/api/conversations",
            json={"title": "API测试"},
            timeout=10
        )
        
        if response.status_code == 201:
            conversation = response.json()
            conversation_id = conversation['conversation_id']
            print(f"✅ 成功创建对话: {conversation_id}")
            
            # 发送消息测试
            message_data = {
                "content": "你好，这是一个测试消息",
                "type": "user"
            }
            
            message_response = requests.post(
                f"http://localhost:3000/api/conversations/{conversation_id}/messages",
                json=message_data,
                timeout=10
            )
            
            if message_response.status_code == 200:
                print("✅ 消息发送成功")
                return True
            else:
                print(f"⚠️  消息发送失败: {message_response.status_code}")
                return False
        else:
            print(f"❌ 创建对话失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    print("🚀 开始测试OpenHands LLM配置...")
    print("=" * 50)
    
    # 测试容器环境变量
    env_ok = test_container_env()
    print()
    
    # 测试Groq API
    groq_ok = test_groq_api()
    print()
    
    # 测试OpenHands API
    api_ok = test_openhands_api()
    print()
    
    print("📊 测试结果:")
    print(f"容器环境变量: {'✅ 正常' if env_ok else '❌ 异常'}")
    print(f"Groq API连接: {'✅ 正常' if groq_ok else '❌ 异常'}")
    print(f"OpenHands API: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if env_ok and groq_ok and api_ok:
        print("\n🎉 所有测试通过！OpenHands LLM配置正常！")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    exit(main())

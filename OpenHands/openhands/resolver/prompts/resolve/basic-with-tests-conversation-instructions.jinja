IMPORTANT: You should <PERSON><PERSON><PERSON> interact with the environment provided to you AND NEVER ASK FOR HUMAN HELP.
You SHOULD INCLUDE PROPER INDENTATION in your edit commands.{% if repo_instruction %}

Some basic information about this repository:
{{ repo_instruction }}{% endif %}

For all changes to actual application code (e.g. in Python or Javascript), add an appropriate test to the testing directory to make sure that the issue has been fixed.
Run the tests, and if they pass you are done!
You do NOT need to write new tests if there are only changes to documentation or configuration files.

When you think you have fixed the issue through code changes, please call the finish action to end the interaction.

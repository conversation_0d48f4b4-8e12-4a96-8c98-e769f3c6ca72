name: Resolve Issue with OpenHands

on:
  issues:
    types: [labeled]
  pull_request:
    types: [labeled]
  issue_comment:
    types: [created]
  pull_request_review_comment:
    types: [created]
  pull_request_review:
    types: [submitted]

permissions:
  contents: write
  pull-requests: write
  issues: write

jobs:
  call-openhands-resolver:
    uses: All-Hands-AI/OpenHands/.github/workflows/openhands-resolver.yml@main
    with:
      macro: ${{ vars.OPENHANDS_MACRO || '@openhands-agent' }}
      max_iterations: ${{ fromJson(vars.OPENHANDS_MAX_ITER || 50) }}
      base_container_image: ${{ vars.OPENHANDS_BASE_CONTAINER_IMAGE || '' }}
      LLM_MODEL: ${{ vars.LLM_MODEL || 'anthropic/claude-sonnet-4-20250514' }}
      target_branch: ${{ vars.TARGET_BRANCH || 'main' }}
      runner: ${{ vars.TARGET_RUNNER }}
    secrets:
      PAT_TOKEN: ${{ secrets.PAT_TOKEN }}
      PAT_USERNAME: ${{ secrets.PAT_USERNAME }}
      LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
      LLM_BASE_URL: ${{ secrets.LLM_BASE_URL }}

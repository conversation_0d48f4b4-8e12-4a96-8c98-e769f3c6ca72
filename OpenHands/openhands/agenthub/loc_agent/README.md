# LocAgent Framework

This folder is an implementation of Locagent. It is based on ([LocAgent](https://arxiv.org/abs/2503.09089), [tweet](https://x.com/XiangruTang/status/1900392655009333338)),  a framework that addresses code localization through graph-based representation. By parsing codebases into directed heterogeneous graphs, LocAgent creates a lightweight representation that captures code structures and their dependencies, enabling LLM agents to effectively search and locate relevant entities through powerful multi-hop reasoning.

<!-- ## Overview -->


## Built-in Tools

The agent provides several built-in tools:

  1. `search_code_snippets`
  2. `get_entity_contents`
  3. `explore_tree_structure`

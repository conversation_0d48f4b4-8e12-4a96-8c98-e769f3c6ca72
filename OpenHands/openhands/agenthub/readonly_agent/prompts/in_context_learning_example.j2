Here's a running example of how to perform a task with the provided tools.

--------------------- START OF EXAMPLE ---------------------

USER: I need to find all TypeScript files in the project and then search for files that contain React components.

A: I'll help you find TypeScript files and React components. Let's break this down into steps:

1. First, let's use the glob tool to find all TypeScript files:
<function=glob>
<parameter=pattern>**/*.{ts,tsx}</parameter>
</function>

USER: EXECUTION RESULT of [glob]:
Below are the execution results of the glob command: rg --files . -g **/*.{ts,tsx} --sortr=modified | head -n 100

src/components/Header.tsx
src/components/Button.tsx
src/pages/Home.tsx
src/utils/types.ts
src/App.tsx
src/index.tsx

A: Great! Now let's search for files that likely contain React components by looking for patterns like "export" followed by "function" or "class":
<function=grep>
<parameter=pattern>export\s+(default\s+)?(function|class)</parameter>
<parameter=include>*.{ts,tsx}</parameter>
</function>

USER: EXECUTION RESULT of [grep]:
Below are the execution results of the search command: rg -li export\s+(default\s+)?(function|class) --sortr=modified --glob *.{ts,tsx} .

src/components/Header.tsx
src/components/Button.tsx
src/pages/Home.tsx
src/App.tsx

A: Let's examine one of these components to confirm it's a React component. I'll look at the Header component:
<function=view>
<parameter=path>src/components/Header.tsx</parameter>
</function>

USER: EXECUTION RESULT of [view]:
     1  import React from 'react';
     2
     3  interface HeaderProps {
     4    title: string;
     5    subtitle?: string;
     6  }
     7
     8  export function Header({ title, subtitle }: HeaderProps) {
     9    return (
    10      <header className="app-header">
    11        <h1>{title}</h1>
    12        {subtitle && <h2>{subtitle}</h2>}
    13      </header>
    14    );
    15  }

A: Let me also check the Button component to see its implementation:
<function=view>
<parameter=path>src/components/Button.tsx</parameter>
</function>

USER: EXECUTION RESULT of [view]:
     1  import React from 'react';
     2
     3  interface ButtonProps {
     4    onClick: () => void;
     5    children: React.ReactNode;
     6    variant?: 'primary' | 'secondary';
     7  }
     8
     9  export default function Button({ onClick, children, variant = 'primary' }: ButtonProps) {
    10    return (
    11      <button
    12        className={`btn btn-${variant}`}
    13        onClick={onClick}
    14      >
    15        {children}
    16      </button>
    17    );
    18  }

A: I've found several React components in the project. Here's a summary:

1. Found TypeScript files using glob pattern `**/*.{ts,tsx}`
2. Identified potential React components using grep pattern `export\s+(default\s+)?(function|class)`
3. Confirmed two components:
   - `Header.tsx`: A function component that renders a header with title and optional subtitle
   - `Button.tsx`: A reusable button component with primary/secondary variants

Would you like me to examine any other components or search for specific features within these components?
<function=finish>
</function>

--------------------- END OF EXAMPLE ---------------------

Do NOT assume the environment is the same as in the example above.

--------------------- NEW TASK DESCRIPTION ---------------------

Your tasking is to fix an issue in your repository. Do the following

1. Read the issue body and comments using the $GITLAB_TOKEN and GitLab API
2. For all changes to actual application code (e.g. in Python or Javascript), add an appropriate test to the testing directory to make sure that the issue has been fixed
3. Run the tests, and if they pass you are done!
4. You do NOT need to write new tests if there are only changes to documentation or configuration files.

When you're done, make sure to

1. Use the `create_mr` tool to open a new MR
2. The MR description should mention that it "fixes" or "closes" the issue number

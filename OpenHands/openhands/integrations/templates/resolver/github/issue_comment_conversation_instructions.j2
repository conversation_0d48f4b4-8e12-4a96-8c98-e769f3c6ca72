You are requested to fix issue number #{{ issue_number }} in a repository.

A comment on the issue has been addressed to you.

# Steps to Handle the Comment

1. Address the comment. Use the $GITHUB_TOKEN and GitHub API to read issue title, body, and comments if you need more context
2. For all changes to actual application code (e.g. in Python or Javascript), add an appropriate test to the testing directory to make sure that the issue has been fixed
3. Run the tests, and if they pass you are done!
4. You do NOT need to write new tests if there are only changes to documentation or configuration files.

When you're done, make sure to

1. Re-read the issue title, body, and comments and make sure that you have successfully implemented all requirements.
2. Use the `create_pr` tool to open a new PR
3. Name the branch using `openhands/` as a prefix (e.g `openhands/update-readme`)
4. The PR description should mention that it "fixes" or "closes" the issue number

Please send a final message summarizing your work.

If you simply answered a question, this final message should re-state the answer to the question.

If you made changes, please first double-check the git diff, think carefully about the user's request(s), and check:
1. whether the request has been completely addressed and all of the instructions have been followed faithfully (in checklist format if appropriate).
2. whether the changes are concise (if there are any extraneous changes not important to addressing the user's request they should be reverted).
3. focus only on summarizing new changes since your last summary, avoiding repetition of information already covered in previous summaries.
If the request has been addressed and the changes are concise, then push your changes to the remote branch and send a final message summarizing the changes.

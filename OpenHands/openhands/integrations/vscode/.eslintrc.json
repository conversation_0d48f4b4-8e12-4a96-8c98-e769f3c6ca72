{
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "project": "./tsconfig.json",
    "ecmaVersion": 2020,
    "sourceType": "module"
  },
  "extends": [
    "airbnb-base",
    "airbnb-typescript/base",
    "prettier",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  "plugins": ["prettier", "unused-imports"],
  "rules": {
    "unused-imports/no-unused-imports": "error",
    "prettier/prettier": ["error"],
    // Resolves https://stackoverflow.com/questions/59265981/typescript-eslint-missing-file-extension-ts-import-extensions/59268871#59268871
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        "": "never",
        "ts": "never"
      }
    ],
    // Allow state modification in reduce and similar patterns
    "no-param-reassign": [
      "error",
      {
        "props": true,
        "ignorePropertyModificationsFor": ["acc", "state"]
      }
    ],
    // For https://stackoverflow.com/questions/55844608/stuck-with-eslint-error-i-e-separately-loops-should-be-avoided-in-favor-of-arra
    "no-restricted-syntax": "off",
    "import/prefer-default-export": "off",
    "no-underscore-dangle": "off",
    "import/no-extraneous-dependencies": "off",
    // VSCode extension specific - allow console for debugging
    "no-console": "warn",
    // Allow leading underscores for private variables in VSCode extensions
    "@typescript-eslint/naming-convention": [
      "error",
      {
        "selector": "variable",
        "format": ["camelCase", "PascalCase", "UPPER_CASE"],
        "leadingUnderscore": "allow"
      }
    ]
  },
  "env": {
    "node": true,
    "es6": true
  },
  "overrides": [
    {
      "files": ["src/test/**/*.ts"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "off",
        "no-console": "off",
        "@typescript-eslint/no-shadow": "off",
        "consistent-return": "off"
      }
    }
  ]
}

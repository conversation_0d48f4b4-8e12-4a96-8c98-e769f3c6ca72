{"name": "openhands-memory-monitor", "displayName": "OpenHands Memory Monitor", "description": "A VSCode extension for monitoring system and process memory usage", "version": "0.1.0", "publisher": "openhands", "engines": {"vscode": "^1.98.2"}, "categories": ["Other", "Monitoring"], "activationEvents": ["onStartupFinished"], "main": "./extension.js", "contributes": {"commands": [{"command": "openhands-memory-monitor.startMemoryMonitor", "title": "Start Memory Monitor"}, {"command": "openhands-memory-monitor.stopMemoryMonitor", "title": "Stop Memory Monitor"}, {"command": "openhands-memory-monitor.showMemoryDetails", "title": "Show Memory Details"}], "menus": {"commandPalette": [{"command": "openhands-memory-monitor.startMemoryMonitor", "group": "OpenHands"}, {"command": "openhands-memory-monitor.stopMemoryMonitor", "group": "OpenHands"}, {"command": "openhands-memory-monitor.showMemoryDetails", "group": "OpenHands"}]}}}
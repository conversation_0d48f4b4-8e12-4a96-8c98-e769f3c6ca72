#!/usr/bin/env python3
"""
测试OpenHands前端状态的Playwright脚本
"""
import asyncio
from playwright.async_api import async_playwright
import sys

async def test_frontend():
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            print("🔍 测试OpenHands前端状态...")
            
            # 访问主页
            print("📍 访问主页: https://ai.guiyunai.fun")
            response = await page.goto("https://ai.guiyunai.fun", timeout=10000)
            print(f"状态码: {response.status}")
            
            if response.status == 502:
                print("❌ 502 Bad Gateway - 后端服务器连接失败")
                return False
            elif response.status != 200:
                print(f"❌ HTTP错误: {response.status}")
                return False
            
            # 等待页面加载
            await page.wait_for_load_state('networkidle', timeout=5000)
            
            # 检查页面标题
            title = await page.title()
            print(f"页面标题: {title}")
            
            # 检查是否有错误信息
            error_elements = await page.query_selector_all('[class*="error"], [class*="Error"]')
            if error_elements:
                print("❌ 发现错误元素")
                for elem in error_elements:
                    text = await elem.text_content()
                    print(f"错误信息: {text}")
            
            # 尝试访问新对话页面
            print("📍 访问新对话页面: https://ai.guiyunai.fun/conversations/new")
            response2 = await page.goto("https://ai.guiyunai.fun/conversations/new", timeout=10000)
            print(f"新对话页面状态码: {response2.status}")
            
            if response2.status == 502:
                print("❌ 新对话页面也返回502")
                return False
            
            # 等待页面加载
            await page.wait_for_load_state('networkidle', timeout=5000)
            
            # 检查是否有"等待运行时启动"的文本
            waiting_text = await page.query_selector('text="等待运行时启动"')
            if waiting_text:
                print("⚠️  发现'等待运行时启动'文本")
                return False
            
            # 检查是否有输入框
            input_box = await page.query_selector('input[type="text"], textarea')
            if input_box:
                print("✅ 找到输入框，前端基本正常")
                return True
            else:
                print("⚠️  未找到输入框")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中出现异常: {e}")
            return False
        finally:
            await browser.close()

async def test_api_directly():
    """直接测试API端点"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            print("🔍 直接测试API端点...")
            
            # 测试API端点
            response = await page.goto("https://ai.guiyunai.fun/api/conversations", timeout=10000)
            print(f"API状态码: {response.status}")
            
            if response.status == 200:
                content = await page.content()
                if "conversation_id" in content:
                    print("✅ API正常工作")
                    return True
                else:
                    print("⚠️  API响应格式异常")
                    return False
            else:
                print(f"❌ API错误: {response.status}")
                return False
                
        except Exception as e:
            print(f"❌ API测试异常: {e}")
            return False
        finally:
            await browser.close()

async def main():
    print("🚀 开始测试OpenHands前端状态...")
    
    # 测试API
    api_ok = await test_api_directly()
    
    # 测试前端
    frontend_ok = await test_frontend()
    
    print("\n📊 测试结果:")
    print(f"API状态: {'✅ 正常' if api_ok else '❌ 异常'}")
    print(f"前端状态: {'✅ 正常' if frontend_ok else '❌ 异常'}")
    
    if api_ok and frontend_ok:
        print("🎉 OpenHands服务正常运行!")
        return 0
    else:
        print("⚠️  OpenHands服务存在问题")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

[core]
workspace_base="/www/wwwroot/ai.guiyunai.fun/workspace"
max_iterations=50
debug=false
disable_color=false
enable_browser=true
max_budget_per_task=0.0
workspace_mount_path_in_sandbox="/workspace"
run_as_openhands=true
runtime="docker"
default_agent="CodeActAgent"
enable_default_condenser=true
max_concurrent_conversations=3
conversation_max_age_seconds=864000

[llm]
model="groq/llama-3.3-70b-versatile"
api_key="********************************************************"
base_url="https://api.groq.com/openai/v1"
max_message_chars=10000
max_input_tokens=0
max_output_tokens=0
num_retries=8
retry_max_wait=120
retry_min_wait=15
retry_multiplier=2.0
drop_params=false
modify_params=true
caching_prompt=true
temperature=0.0
timeout=0
top_p=1.0
disable_vision=false

[agent]
enable_browsing=true
enable_llm_editor=false
enable_editor=true
enable_jupyter=true
enable_cmd=true
enable_think=true
enable_finish=true
enable_prompt_extensions=true
enable_history_truncation=true
enable_condensation_request=false

[sandbox]
timeout=300
user_id=1000
base_container_image="nikolaik/python-nodejs:python3.12-nodejs22"
use_host_network=false
enable_auto_lint=false
initialize_plugins=true
runtime_container_image="docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik"
keep_runtime_alive=true
pause_closed_runtimes=true
close_delay=300
rm_all_containers=false
enable_gpu=false
max_runtime_containers=3

[security]
confirmation_mode=false

[condenser]
type="noop"

[mcp]
sse_servers = [
    "http://localhost:8080/sse",
    "http://localhost:8083/sse"
]
stdio_servers = []

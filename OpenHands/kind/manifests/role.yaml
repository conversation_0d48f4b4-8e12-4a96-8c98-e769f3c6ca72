---
# mirrord-rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: mirrord-role
  namespace: default
rules:
  - apiGroups: [""]
    resources: ["pods", "pods/exec", "pods/portforward", "services", "persistentvolumeclaims"]
    verbs: ["get", "list", "create", "delete", "watch", "update"]
  - apiGroups: ["networking.k8s.io"] # Networking API group (for ingress, networkpolicies, etc.)
    resources: ["ingresses", "networkpolicies"]
    verbs: ["get", "list", "create", "delete", "watch", "update"]

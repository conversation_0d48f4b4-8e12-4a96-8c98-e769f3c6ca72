#!/bin/bash

# 设置环境变量 - Groq配置
export SANDBOX_USER_ID=1000
export LLM_API_KEY="********************************************************"
export LLM_MODEL="groq/llama-3.3-70b-versatile"
export LLM_BASE_URL="https://api.groq.com/openai/v1"
export RUNTIME="docker"
export WORKSPACE_BASE="/www/wwwroot/ai.guiyunai.fun/workspace"

echo "启动OpenHands后端服务..."
echo "LLM模型: $LLM_MODEL"
echo "API端点: $LLM_BASE_URL"

# 启动后端服务
/root/.cache/pypoetry/virtualenvs/openhands-ai-qoVAMGzs-py3.12/bin/python -m uvicorn openhands.server.listen:app --host 127.0.0.1 --port 3000

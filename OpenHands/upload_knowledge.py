#!/usr/bin/env python3
"""
知识库上传工具
用于向中国传统文化知识库上传专业资料
"""

import sys
import json
from mcp_servers.simple_yijing_server import SimpleYijingServer

def upload_yijing_knowledge():
    """上传易经知识示例"""
    server = SimpleYijingServer()
    
    # 示例：上传易经基础知识
    yijing_content = """
# 易经基础知识

## 八卦基础
八卦是易经的基础符号系统，由三个爻组成，分为：

### 乾卦 ☰
- 符号：三个阳爻
- 属性：天、刚健、创造
- 方位：西北
- 家庭：父亲
- 季节：秋冬之交

### 坤卦 ☷  
- 符号：三个阴爻
- 属性：地、柔顺、承载
- 方位：西南
- 家庭：母亲
- 季节：夏秋之交

### 震卦 ☳
- 符号：下阳上阴阴
- 属性：雷、动、奋起
- 方位：正东
- 家庭：长男
- 季节：春季

### 巽卦 ☴
- 符号：下阴阴上阳
- 属性：风、入、顺从
- 方位：东南
- 家庭：长女
- 季节：春夏之交

### 坎卦 ☵
- 符号：中阳两边阴
- 属性：水、险、陷
- 方位：正北
- 家庭：中男
- 季节：冬季

### 离卦 ☲
- 符号：中阴两边阳
- 属性：火、明、丽
- 方位：正南
- 家庭：中女
- 季节：夏季

### 艮卦 ☶
- 符号：上阳下阴阴
- 属性：山、止、静
- 方位：东北
- 家庭：少男
- 季节：冬春之交

### 兑卦 ☱
- 符号：上阴下阳阳
- 属性：泽、悦、说
- 方位：正西
- 家庭：少女
- 季节：秋季

## 六十四卦的构成
六十四卦是由两个八卦上下组合而成：
- 上卦（外卦）：代表外在环境、他人、未来
- 下卦（内卦）：代表内在状态、自己、现在

## 爻的含义
每一卦有六个爻位，从下往上数：
- 初爻：基础、开始
- 二爻：发展、中间
- 三爻：转折、危险
- 四爻：接近、谨慎  
- 五爻：成功、君位
- 上爻：极限、终结

## 占卜的基本原理
易经占卜基于"天人合一"的思想：
1. 万物皆有规律可循
2. 变化是永恒的主题
3. 阴阳平衡是和谐的基础
4. 时机的把握至关重要
"""
    
    result = server.upload_knowledge(
        filename="易经基础知识.md",
        content=yijing_content,
        category="基础理论",
        description="易经八卦基础知识，包含八卦属性、六十四卦构成、爻位含义等"
    )
    
    print("上传易经基础知识:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 上传实用指导
    practical_content = """
# 易经在现代生活中的应用

## 决策指导
当面临重要决策时，可以参考易经的智慧：

### 1. 时机把握
- 观察环境变化
- 分析内外因素
- 选择合适时机行动

### 2. 平衡思维
- 考虑阴阳两面
- 寻求中庸之道
- 避免极端选择

### 3. 变化适应
- 接受变化的必然性
- 主动适应环境
- 在变化中寻找机会

## 人际关系
易经对人际关系的指导：

### 谦卦的启示
- 保持谦逊态度
- 尊重他人意见
- 以德服人

### 泰卦的智慧
- 上下和谐
- 沟通顺畅
- 互利共赢

## 事业发展
根据易经原理指导事业：

### 乾卦精神
- 自强不息
- 积极进取
- 承担责任

### 坤卦品质
- 厚德载物
- 包容合作
- 服务他人

## 学习成长
易经对个人成长的启发：

### 蒙卦教育观
- 主动求学
- 尊师重道
- 循序渐进

### 渐卦发展观
- 稳步前进
- 不急不躁
- 持续改进
"""
    
    result2 = server.upload_knowledge(
        filename="易经现代应用指南.md",
        content=practical_content,
        category="实用指导",
        description="易经智慧在现代生活中的具体应用，包括决策、人际关系、事业发展等方面"
    )
    
    print("\n上传现代应用指南:")
    print(json.dumps(result2, ensure_ascii=False, indent=2))
    
    # 测试搜索功能
    print("\n=== 测试搜索功能 ===")
    search_result = server.search_knowledge("决策")
    print("搜索'决策':")
    print(json.dumps(search_result, ensure_ascii=False, indent=2))

def show_statistics():
    """显示知识库统计"""
    server = SimpleYijingServer()
    stats = server.get_statistics()
    print("知识库统计信息:")
    print(json.dumps(stats, ensure_ascii=False, indent=2))

def search_knowledge(query):
    """搜索知识库"""
    server = SimpleYijingServer()
    result = server.search_knowledge(query)
    print(f"搜索结果 '{query}':")
    print(json.dumps(result, ensure_ascii=False, indent=2))

def query_hexagram(name):
    """查询卦象"""
    server = SimpleYijingServer()
    result = server.query_hexagram(name)
    print(f"查询卦象 '{name}':")
    print(json.dumps(result, ensure_ascii=False, indent=2))

def divination(question, hexagram_num):
    """占卜分析"""
    server = SimpleYijingServer()
    result = server.divination_analysis(question, int(hexagram_num))
    print(f"占卜分析:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 upload_knowledge.py <command> [args...]")
        print("\n可用命令:")
        print("  upload     - 上传示例知识")
        print("  stats      - 显示统计信息")
        print("  search <query>  - 搜索知识库")
        print("  query <name>    - 查询卦象")
        print("  divine <question> <hexagram_number> - 占卜分析")
        print("\n示例:")
        print("  python3 upload_knowledge.py upload")
        print("  python3 upload_knowledge.py search 决策")
        print("  python3 upload_knowledge.py query 乾")
        print("  python3 upload_knowledge.py divine '我的事业如何？' 1")
        return
    
    command = sys.argv[1]
    
    if command == "upload":
        upload_yijing_knowledge()
    elif command == "stats":
        show_statistics()
    elif command == "search" and len(sys.argv) > 2:
        search_knowledge(sys.argv[2])
    elif command == "query" and len(sys.argv) > 2:
        query_hexagram(sys.argv[2])
    elif command == "divine" and len(sys.argv) > 3:
        question = sys.argv[2]
        hexagram_num = sys.argv[3]
        divination(question, hexagram_num)
    else:
        print("无效的命令或缺少参数")
        print("使用 'python3 upload_knowledge.py' 查看帮助")

if __name__ == "__main__":
    main()

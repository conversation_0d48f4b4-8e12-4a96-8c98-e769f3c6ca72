#!/usr/bin/env python3
"""
知识库管理工具
用于上传、管理和维护中国传统文化知识库
"""

import os
import json
import sqlite3
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

class KnowledgeManager:
    """知识库管理器"""
    
    def __init__(self):
        self.base_path = "/data"
        self.upload_path = "/data/uploads"
        self.knowledge_path = "/data/knowledge"
        
        # 确保目录存在
        for path in [self.base_path, self.upload_path, self.knowledge_path]:
            os.makedirs(path, exist_ok=True)
            
        # 各领域的数据库路径
        self.databases = {
            "yijing": "/data/yijing/knowledge.db",
            "fengshui": "/data/fengshui/rules.db", 
            "tcm": "/data/tcm/herbs.db",
            "heritage": "/data/heritage/unesco_china.db"
        }

    def upload_document(self, domain: str, filename: str, content: str, 
                       category: str = "general", description: str = "") -> Dict[str, Any]:
        """
        上传文档到指定领域的知识库
        
        Args:
            domain: 领域 (yijing, fengshui, tcm, heritage)
            filename: 文件名
            content: 文件内容
            category: 分类
            description: 描述
        """
        if domain not in self.databases:
            return {"错误": f"不支持的领域: {domain}"}
        
        try:
            # 计算文件哈希
            file_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
            
            # 保存原始文件
            domain_upload_path = os.path.join(self.upload_path, domain)
            os.makedirs(domain_upload_path, exist_ok=True)
            
            file_path = os.path.join(domain_upload_path, f"{file_hash}_{filename}")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 处理并存储到对应数据库
            result = self._process_document(domain, filename, content, category, description, file_hash)
            
            return {
                "状态": "成功",
                "领域": domain,
                "文件": filename,
                "文件路径": file_path,
                "处理结果": result
            }
            
        except Exception as e:
            return {
                "状态": "失败",
                "错误": str(e)
            }

    def _process_document(self, domain: str, filename: str, content: str, 
                         category: str, description: str, file_hash: str) -> Dict[str, Any]:
        """处理文档内容并存储到相应数据库"""
        
        if domain == "yijing":
            return self._process_yijing_document(filename, content, category, description, file_hash)
        elif domain == "fengshui":
            return self._process_fengshui_document(filename, content, category, description, file_hash)
        elif domain == "tcm":
            return self._process_tcm_document(filename, content, category, description, file_hash)
        elif domain == "heritage":
            return self._process_heritage_document(filename, content, category, description, file_hash)
        else:
            return {"错误": "未知领域"}

    def _process_yijing_document(self, filename: str, content: str, category: str, 
                                description: str, file_hash: str) -> Dict[str, Any]:
        """处理易经文档"""
        db_path = self.databases["yijing"]
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # 记录上传文档
            cursor.execute('''
                INSERT OR REPLACE INTO uploaded_documents 
                (filename, file_hash, content_type, category, description)
                VALUES (?, ?, ?, ?, ?)
            ''', (filename, file_hash, "text/plain", category, description))
            
            document_id = cursor.lastrowid
            
            # 解析易经内容
            entries = self._parse_yijing_content(content, category)
            
            # 存储知识条目
            for entry in entries:
                cursor.execute('''
                    INSERT INTO knowledge_entries 
                    (document_id, title, content, category, tags, confidence_score)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (document_id, entry['title'], entry['content'], 
                     entry['category'], entry['tags'], entry['confidence']))
            
            conn.commit()
            return {
                "文档ID": document_id,
                "提取条目": len(entries),
                "类型": "易经知识"
            }
            
        finally:
            conn.close()

    def _parse_yijing_content(self, content: str, category: str) -> List[Dict[str, Any]]:
        """解析易经内容"""
        entries = []
        
        # 按段落分割
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph) > 10:
                # 检测是否包含卦名
                hexagram_names = ["乾", "坤", "屯", "蒙", "需", "讼", "师", "比", "小畜", "履"]  # 可扩展
                
                title = f"{category}_条目_{i+1}"
                tags = []
                
                # 提取卦名标签
                for name in hexagram_names:
                    if name in paragraph:
                        tags.append(name)
                        title = f"{name}卦相关内容"
                        break
                
                # 提取其他关键词
                keywords = ["爻", "象", "彖", "文言", "系辞", "说卦", "序卦", "杂卦"]
                for keyword in keywords:
                    if keyword in paragraph:
                        tags.append(keyword)
                
                entry = {
                    'title': title,
                    'content': paragraph,
                    'category': category,
                    'tags': ",".join(tags[:5]),
                    'confidence': 0.9 if tags else 0.7
                }
                entries.append(entry)
        
        return entries

    def list_documents(self, domain: str) -> Dict[str, Any]:
        """列出指定领域的所有文档"""
        if domain not in self.databases:
            return {"错误": f"不支持的领域: {domain}"}
        
        db_path = self.databases[domain]
        if not os.path.exists(db_path):
            return {"文档": [], "总数": 0}
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT filename, category, description, upload_time, processed
                FROM uploaded_documents
                ORDER BY upload_time DESC
            ''')
            
            documents = []
            for row in cursor.fetchall():
                documents.append({
                    "文件名": row[0],
                    "分类": row[1],
                    "描述": row[2],
                    "上传时间": row[3],
                    "已处理": "是" if row[4] else "否"
                })
            
            return {
                "领域": domain,
                "文档": documents,
                "总数": len(documents)
            }
            
        finally:
            conn.close()

    def search_knowledge(self, domain: str, query: str, limit: int = 10) -> Dict[str, Any]:
        """搜索知识库"""
        if domain not in self.databases:
            return {"错误": f"不支持的领域: {domain}"}
        
        db_path = self.databases[domain]
        if not os.path.exists(db_path):
            return {"结果": [], "总数": 0}
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT ke.title, ke.content, ke.category, ke.tags, 
                       ud.filename, ke.confidence_score
                FROM knowledge_entries ke
                JOIN uploaded_documents ud ON ke.document_id = ud.id
                WHERE ke.content LIKE ? OR ke.title LIKE ? OR ke.tags LIKE ?
                ORDER BY ke.confidence_score DESC
                LIMIT ?
            ''', (f'%{query}%', f'%{query}%', f'%{query}%', limit))
            
            results = []
            for row in cursor.fetchall():
                results.append({
                    "标题": row[0],
                    "内容": row[1][:200] + "..." if len(row[1]) > 200 else row[1],
                    "分类": row[2],
                    "标签": row[3],
                    "来源": row[4],
                    "置信度": row[5]
                })
            
            return {
                "查询": query,
                "领域": domain,
                "结果": results,
                "总数": len(results)
            }
            
        finally:
            conn.close()

    def export_knowledge(self, domain: str, format: str = "json") -> Dict[str, Any]:
        """导出知识库"""
        if domain not in self.databases:
            return {"错误": f"不支持的领域: {domain}"}
        
        db_path = self.databases[domain]
        if not os.path.exists(db_path):
            return {"错误": "数据库不存在"}
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # 导出所有知识条目
            cursor.execute('''
                SELECT ke.title, ke.content, ke.category, ke.tags, 
                       ud.filename, ud.upload_time, ke.confidence_score
                FROM knowledge_entries ke
                JOIN uploaded_documents ud ON ke.document_id = ud.id
                ORDER BY ke.confidence_score DESC
            ''')
            
            entries = []
            for row in cursor.fetchall():
                entries.append({
                    "title": row[0],
                    "content": row[1],
                    "category": row[2],
                    "tags": row[3].split(",") if row[3] else [],
                    "source_file": row[4],
                    "upload_time": row[5],
                    "confidence": row[6]
                })
            
            # 保存导出文件
            export_filename = f"{domain}_knowledge_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            export_path = os.path.join(self.knowledge_path, export_filename)
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "domain": domain,
                    "export_time": datetime.now().isoformat(),
                    "total_entries": len(entries),
                    "entries": entries
                }, f, ensure_ascii=False, indent=2)
            
            return {
                "状态": "成功",
                "导出文件": export_path,
                "条目数量": len(entries)
            }
            
        finally:
            conn.close()

    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        stats = {}
        
        for domain, db_path in self.databases.items():
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                try:
                    # 文档数量
                    cursor.execute('SELECT COUNT(*) FROM uploaded_documents')
                    doc_count = cursor.fetchone()[0]
                    
                    # 知识条目数量
                    cursor.execute('SELECT COUNT(*) FROM knowledge_entries')
                    entry_count = cursor.fetchone()[0]
                    
                    # 分类统计
                    cursor.execute('''
                        SELECT category, COUNT(*) 
                        FROM knowledge_entries 
                        GROUP BY category
                    ''')
                    categories = dict(cursor.fetchall())
                    
                    stats[domain] = {
                        "文档数量": doc_count,
                        "知识条目": entry_count,
                        "分类统计": categories
                    }
                    
                finally:
                    conn.close()
            else:
                stats[domain] = {
                    "文档数量": 0,
                    "知识条目": 0,
                    "分类统计": {}
                }
        
        return stats

def main():
    """命令行工具"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python knowledge_manager.py <command> [args...]")
        print("命令:")
        print("  stats - 显示统计信息")
        print("  list <domain> - 列出文档")
        print("  search <domain> <query> - 搜索知识")
        print("  export <domain> - 导出知识库")
        return
    
    manager = KnowledgeManager()
    command = sys.argv[1]
    
    if command == "stats":
        stats = manager.get_statistics()
        print(json.dumps(stats, ensure_ascii=False, indent=2))
    
    elif command == "list" and len(sys.argv) > 2:
        domain = sys.argv[2]
        result = manager.list_documents(domain)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    
    elif command == "search" and len(sys.argv) > 3:
        domain = sys.argv[2]
        query = sys.argv[3]
        result = manager.search_knowledge(domain, query)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    
    elif command == "export" and len(sys.argv) > 2:
        domain = sys.argv[2]
        result = manager.export_knowledge(domain)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    
    else:
        print("无效的命令或参数")

if __name__ == "__main__":
    main()

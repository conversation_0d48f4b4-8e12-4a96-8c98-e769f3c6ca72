#!/usr/bin/env python3
"""
简化版易经八卦知识库MCP服务器
用于测试和基本功能演示
"""

import json
import sqlite3
import os
from datetime import datetime
from typing import Dict, List, Any

class SimpleYijingServer:
    def __init__(self):
        self.db_path = os.getenv("YIJING_DB_PATH", "/data/yijing/knowledge.db")
        self.upload_path = os.getenv("UPLOAD_PATH", "/data/uploads/yijing")
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        os.makedirs(self.upload_path, exist_ok=True)
        
        self.init_database()
        
    def init_database(self):
        """初始化易经知识数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建六十四卦表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS hexagrams (
                id INTEGER PRIMARY KEY,
                number INTEGER UNIQUE,
                name_chinese TEXT,
                name_pinyin TEXT,
                upper_trigram TEXT,
                lower_trigram TEXT,
                judgment TEXT,
                image TEXT,
                interpretation TEXT,
                modern_application TEXT,
                source TEXT DEFAULT '系统内置',
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建用户上传文档表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS uploaded_documents (
                id INTEGER PRIMARY KEY,
                filename TEXT,
                content TEXT,
                category TEXT,
                description TEXT,
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入基础的六十四卦数据
        self._insert_basic_hexagrams(cursor)
        
        conn.commit()
        conn.close()

    def _insert_basic_hexagrams(self, cursor):
        """插入基础的六十四卦数据"""
        basic_hexagrams = [
            (1, "乾", "qian", "乾", "乾", "元亨利贞", "天行健，君子以自强不息", 
             "象征创造力、领导力、积极进取的精神", "适用于事业发展、领导管理、创新创业"),
            (2, "坤", "kun", "坤", "坤", "元亨，利牝马之贞", "地势坤，君子以厚德载物", 
             "象征包容、承载、柔顺配合的品质", "适用于团队合作、人际关系、服务他人"),
            (3, "屯", "zhun", "坎", "震", "元亨利贞，勿用有攸往", "云雷屯，君子以经纶", 
             "象征初创困难、需要积累实力", "适用于创业初期、新项目启动、困难时期"),
            (4, "蒙", "meng", "艮", "坎", "亨。匪我求童蒙，童蒙求我", "山下出泉，蒙", 
             "象征启蒙教育、学习成长", "适用于教育培训、学习进修、指导他人"),
            (5, "需", "xu", "坎", "乾", "有孚，光亨，贞吉", "云上于天，需", 
             "象征等待时机、耐心准备", "适用于等待机会、准备阶段、耐心积累"),
            (6, "讼", "song", "乾", "坎", "有孚，窒惕，中吉", "天与水违行，讼", 
             "象征争讼纠纷、需要谨慎", "适用于法律纠纷、冲突解决、谨慎决策"),
            (7, "师", "shi", "坤", "坎", "贞，丈人吉，无咎", "地中有水，师", 
             "象征军队组织、领导统御", "适用于团队管理、组织建设、统一行动"),
            (8, "比", "bi", "坎", "坤", "吉。原筮元永贞", "水在地上，比", 
             "象征亲近团结、相互依靠", "适用于合作关系、联盟建立、亲密关系")
        ]
        
        for hexagram in basic_hexagrams:
            cursor.execute('''
                INSERT OR IGNORE INTO hexagrams 
                (number, name_chinese, name_pinyin, upper_trigram, lower_trigram, 
                 judgment, image, interpretation, modern_application)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', hexagram)

    def query_hexagram(self, query: str, method: str = "name") -> Dict[str, Any]:
        """查询六十四卦信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            if method == "name":
                cursor.execute('''
                    SELECT * FROM hexagrams 
                    WHERE name_chinese LIKE ? OR name_pinyin LIKE ?
                    ORDER BY number
                ''', (f'%{query}%', f'%{query}%'))
            elif method == "number":
                cursor.execute('SELECT * FROM hexagrams WHERE number = ?', (int(query),))
            elif method == "situation":
                cursor.execute('''
                    SELECT * FROM hexagrams 
                    WHERE interpretation LIKE ? OR modern_application LIKE ?
                    ORDER BY number
                ''', (f'%{query}%', f'%{query}%'))
            
            result = cursor.fetchone()
            
            if result:
                return {
                    "状态": "成功",
                    "卦号": result[1],
                    "卦名": result[2],
                    "拼音": result[3], 
                    "上卦": result[4],
                    "下卦": result[5],
                    "卦辞": result[6],
                    "象辞": result[7],
                    "解释": result[8],
                    "现代应用": result[9],
                    "来源": result[10] or "系统内置"
                }
            else:
                return {
                    "状态": "未找到",
                    "错误": "未找到相关卦象", 
                    "建议": "请检查查询内容或尝试其他查询方式"
                }
                
        except Exception as e:
            return {
                "状态": "错误",
                "错误": str(e)
            }
        finally:
            conn.close()

    def upload_knowledge(self, filename: str, content: str, category: str = "general", description: str = "") -> Dict[str, Any]:
        """上传易经相关知识文档"""
        try:
            # 保存到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO uploaded_documents 
                (filename, content, category, description)
                VALUES (?, ?, ?, ?)
            ''', (filename, content, category, description))
            
            document_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            # 保存文件
            file_path = os.path.join(self.upload_path, f"{document_id}_{filename}")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return {
                "状态": "成功",
                "文件": filename,
                "文档ID": document_id,
                "文件路径": file_path,
                "消息": f"成功上传 {filename}"
            }
            
        except Exception as e:
            return {
                "状态": "失败",
                "错误": str(e)
            }

    def search_knowledge(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """搜索知识库内容"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 搜索上传的文档
            cursor.execute('''
                SELECT filename, content, category, description, upload_time
                FROM uploaded_documents
                WHERE content LIKE ? OR filename LIKE ? OR description LIKE ?
                ORDER BY upload_time DESC
                LIMIT ?
            ''', (f'%{query}%', f'%{query}%', f'%{query}%', limit))
            
            doc_results = cursor.fetchall()
            
            # 搜索卦象
            cursor.execute('''
                SELECT name_chinese, interpretation, modern_application
                FROM hexagrams
                WHERE name_chinese LIKE ? OR interpretation LIKE ? OR modern_application LIKE ?
                LIMIT ?
            ''', (f'%{query}%', f'%{query}%', f'%{query}%', limit))
            
            hexagram_results = cursor.fetchall()
            
            results = {
                "查询": query,
                "文档结果": [
                    {
                        "文件名": row[0],
                        "内容预览": row[1][:100] + "..." if len(row[1]) > 100 else row[1],
                        "分类": row[2],
                        "描述": row[3],
                        "上传时间": row[4]
                    } for row in doc_results
                ],
                "卦象结果": [
                    {
                        "卦名": row[0],
                        "解释": row[1],
                        "现代应用": row[2]
                    } for row in hexagram_results
                ],
                "总数": len(doc_results) + len(hexagram_results)
            }
            
            return results
            
        except Exception as e:
            return {
                "状态": "错误",
                "错误": str(e)
            }
        finally:
            conn.close()

    def divination_analysis(self, question: str, hexagram_number: int) -> Dict[str, Any]:
        """占卜分析"""
        # 获取主卦信息
        hexagram_info = self.query_hexagram(str(hexagram_number), "number")
        
        if hexagram_info.get("状态") != "成功":
            return hexagram_info
        
        analysis = {
            "问题": question,
            "主卦": hexagram_info["卦名"],
            "分析时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "卦辞": hexagram_info["卦辞"],
            "象辞": hexagram_info["象辞"],
            "基本解读": f"针对您关于'{question}'的问题，{hexagram_info['卦名']}卦显示：{hexagram_info['解释']}",
            "现代指导": hexagram_info["现代应用"],
            "建议": [
                f"根据{hexagram_info['卦名']}卦的指导，建议您保持积极的心态",
                "注重内在修养，顺应自然规律",
                "在行动前多思考，把握好时机",
                "保持谦逊，与人为善"
            ]
        }
        
        return analysis

    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 卦象数量
            cursor.execute('SELECT COUNT(*) FROM hexagrams')
            hexagram_count = cursor.fetchone()[0]
            
            # 上传文档数量
            cursor.execute('SELECT COUNT(*) FROM uploaded_documents')
            doc_count = cursor.fetchone()[0]
            
            # 分类统计
            cursor.execute('''
                SELECT category, COUNT(*) 
                FROM uploaded_documents 
                GROUP BY category
            ''')
            categories = dict(cursor.fetchall())
            
            return {
                "卦象数量": hexagram_count,
                "上传文档": doc_count,
                "分类统计": categories,
                "数据库路径": self.db_path,
                "上传路径": self.upload_path
            }
            
        finally:
            conn.close()

def main():
    """测试函数"""
    server = SimpleYijingServer()
    
    print("=== 易经知识库MCP服务器测试 ===")
    
    # 测试统计信息
    stats = server.get_statistics()
    print(f"统计信息: {json.dumps(stats, ensure_ascii=False, indent=2)}")
    
    # 测试查询卦象
    result = server.query_hexagram("乾", "name")
    print(f"查询乾卦: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 测试占卜分析
    analysis = server.divination_analysis("我的事业发展如何？", 1)
    print(f"占卜分析: {json.dumps(analysis, ensure_ascii=False, indent=2)}")
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    main()

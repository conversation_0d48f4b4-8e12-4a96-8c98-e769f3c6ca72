#!/usr/bin/env python3
"""
易经八卦知识库MCP服务器
支持用户上传专业资料，提供精确的易经咨询服务
"""

import json
import sqlite3
import os
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# MCP相关导入
from mcp.server import Server
from mcp.types import Tool, TextContent
import mcp.server.stdio

class YijingKnowledgeServer:
    def __init__(self):
        self.db_path = os.getenv("YIJING_DB_PATH", "/data/yijing/knowledge.db")
        self.upload_path = os.getenv("UPLOAD_PATH", "/data/uploads/yijing")
        self.language = os.getenv("LANGUAGE", "zh-CN")
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        os.makedirs(self.upload_path, exist_ok=True)
        
        self.server = Server("yijing-knowledge")
        self.init_database()
        self.setup_tools()
        
    def init_database(self):
        """初始化易经知识数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建六十四卦表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS hexagrams (
                id INTEGER PRIMARY KEY,
                number INTEGER UNIQUE,
                name_chinese TEXT,
                name_pinyin TEXT,
                upper_trigram TEXT,
                lower_trigram TEXT,
                judgment TEXT,
                image TEXT,
                interpretation TEXT,
                modern_application TEXT,
                source TEXT,
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建爻辞表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS yao_lines (
                id INTEGER PRIMARY KEY,
                hexagram_id INTEGER,
                line_number INTEGER,
                line_text TEXT,
                interpretation TEXT,
                source TEXT,
                FOREIGN KEY (hexagram_id) REFERENCES hexagrams (id)
            )
        ''')
        
        # 创建用户上传文档表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS uploaded_documents (
                id INTEGER PRIMARY KEY,
                filename TEXT,
                file_hash TEXT UNIQUE,
                content_type TEXT,
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed BOOLEAN DEFAULT FALSE,
                category TEXT,
                description TEXT
            )
        ''')
        
        # 创建知识条目表（从上传文档中提取）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_entries (
                id INTEGER PRIMARY KEY,
                document_id INTEGER,
                title TEXT,
                content TEXT,
                category TEXT,
                tags TEXT,
                confidence_score REAL,
                FOREIGN KEY (document_id) REFERENCES uploaded_documents (id)
            )
        ''')
        
        # 插入基础的六十四卦数据（如果不存在）
        self._insert_basic_hexagrams(cursor)
        
        conn.commit()
        conn.close()

    def _insert_basic_hexagrams(self, cursor):
        """插入基础的六十四卦数据"""
        basic_hexagrams = [
            (1, "乾", "qian", "乾", "乾", "元亨利贞", "天行健，君子以自强不息", "创造力、领导力、积极进取", "事业发展、领导管理"),
            (2, "坤", "kun", "坤", "坤", "元亨，利牝马之贞", "地势坤，君子以厚德载物", "包容、承载、柔顺配合", "团队合作、人际关系"),
            (3, "屯", "zhun", "坎", "震", "元亨利贞，勿用有攸往", "云雷屯，君子以经纶", "初创困难、积累实力", "创业初期、新项目启动"),
            # 可以继续添加更多卦象...
        ]
        
        for hexagram in basic_hexagrams:
            cursor.execute('''
                INSERT OR IGNORE INTO hexagrams 
                (number, name_chinese, name_pinyin, upper_trigram, lower_trigram, 
                 judgment, image, interpretation, modern_application, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', hexagram + ("系统内置",))

    def setup_tools(self):
        """设置MCP工具"""

        @self.server.tool()
        async def hexagram_query(query: str, method: str = "name") -> str:
            """
            查询六十四卦信息

            Args:
                query: 查询内容（卦名、卦号等）
                method: 查询方式 ("name", "number", "situation")
            """
            result = self._query_hexagram_impl(query, method)
            return json.dumps(result, ensure_ascii=False, indent=2)

        @self.server.tool()
        async def upload_knowledge(filename: str, content: str, category: str = "general", description: str = "") -> str:
            """
            上传易经相关知识文档

            Args:
                filename: 文件名
                content: 文件内容
                category: 分类（hexagram, commentary, philosophy等）
                description: 描述
            """
            result = self._upload_knowledge_impl(filename, content, category, description)
            return json.dumps(result, ensure_ascii=False, indent=2)

        @self.server.tool()
        async def search_knowledge(query: str, category: str = "", limit: int = 10) -> str:
            """
            搜索知识库内容

            Args:
                query: 搜索关键词
                category: 分类筛选
                limit: 返回结果数量限制
            """
            result = self._search_knowledge_impl(query, category, limit)
            return json.dumps(result, ensure_ascii=False, indent=2)

        @self.server.tool()
        async def divination_analysis(question: str, hexagram_number: int, changing_lines: str = "") -> str:
            """
            分析占卜结果

            Args:
                question: 占卜问题
                hexagram_number: 主卦号码
                changing_lines: 变爻位置列表（逗号分隔的数字）
            """
            lines = [int(x.strip()) for x in changing_lines.split(",") if x.strip().isdigit()] if changing_lines else []
            result = self._analyze_divination_impl(question, hexagram_number, lines)
            return json.dumps(result, ensure_ascii=False, indent=2)

    def _query_hexagram_impl(self, query: str, method: str) -> Dict[str, Any]:
        """查询卦象实现"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            if method == "name":
                cursor.execute('''
                    SELECT * FROM hexagrams 
                    WHERE name_chinese LIKE ? OR name_pinyin LIKE ?
                    ORDER BY upload_time DESC
                ''', (f'%{query}%', f'%{query}%'))
            elif method == "number":
                cursor.execute('SELECT * FROM hexagrams WHERE number = ?', (int(query),))
            elif method == "situation":
                cursor.execute('''
                    SELECT * FROM hexagrams 
                    WHERE interpretation LIKE ? OR modern_application LIKE ?
                    ORDER BY upload_time DESC
                ''', (f'%{query}%', f'%{query}%'))
            
            result = cursor.fetchone()
            
            if result:
                hexagram_data = {
                    "卦号": result[1],
                    "卦名": result[2],
                    "拼音": result[3], 
                    "上卦": result[4],
                    "下卦": result[5],
                    "卦辞": result[6],
                    "象辞": result[7],
                    "解释": result[8],
                    "现代应用": result[9],
                    "来源": result[10]
                }
                
                # 获取爻辞
                cursor.execute('''
                    SELECT line_number, line_text, interpretation, source
                    FROM yao_lines WHERE hexagram_id = ?
                    ORDER BY line_number
                ''', (result[0],))
                yao_lines = cursor.fetchall()
                
                hexagram_data["爻辞"] = [
                    {
                        "爻位": f"第{line[0]}爻",
                        "爻辞": line[1], 
                        "解释": line[2],
                        "来源": line[3]
                    } for line in yao_lines
                ]
                
                return hexagram_data
            else:
                return {"错误": "未找到相关卦象", "建议": "请检查查询内容或尝试其他查询方式"}
                
        finally:
            conn.close()

    def _upload_knowledge_impl(self, filename: str, content: str, category: str, description: str) -> Dict[str, Any]:
        """上传知识文档实现"""
        try:
            # 计算文件哈希
            file_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
            
            # 保存文件
            file_path = os.path.join(self.upload_path, f"{file_hash}_{filename}")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 记录到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO uploaded_documents 
                (filename, file_hash, content_type, category, description)
                VALUES (?, ?, ?, ?, ?)
            ''', (filename, file_hash, "text/plain", category, description))
            
            document_id = cursor.lastrowid
            
            # 简单的内容解析和知识提取
            knowledge_entries = self._extract_knowledge_from_content(content, category)
            
            for entry in knowledge_entries:
                cursor.execute('''
                    INSERT INTO knowledge_entries 
                    (document_id, title, content, category, tags, confidence_score)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (document_id, entry['title'], entry['content'], 
                     entry['category'], entry['tags'], entry['confidence']))
            
            conn.commit()
            conn.close()
            
            return {
                "状态": "成功",
                "文件": filename,
                "文档ID": document_id,
                "提取条目": len(knowledge_entries),
                "消息": f"成功上传并处理了 {filename}，提取了 {len(knowledge_entries)} 个知识条目"
            }
            
        except Exception as e:
            return {
                "状态": "失败",
                "错误": str(e),
                "建议": "请检查文件格式和内容"
            }

    def _extract_knowledge_from_content(self, content: str, category: str) -> List[Dict[str, Any]]:
        """从内容中提取知识条目"""
        entries = []
        
        # 简单的文本分段处理
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph) > 20:  # 过滤太短的段落
                entry = {
                    'title': f"{category}_条目_{i+1}",
                    'content': paragraph,
                    'category': category,
                    'tags': self._extract_tags(paragraph),
                    'confidence': 0.8  # 简单的置信度
                }
                entries.append(entry)
        
        return entries

    def _extract_tags(self, text: str) -> str:
        """从文本中提取标签"""
        # 简单的关键词提取
        keywords = []
        
        # 易经相关关键词
        yijing_keywords = ["卦", "爻", "阴", "阳", "乾", "坤", "震", "巽", "坎", "离", "艮", "兑"]
        for keyword in yijing_keywords:
            if keyword in text:
                keywords.append(keyword)
        
        return ",".join(keywords[:5])  # 最多5个标签

    def _search_knowledge_impl(self, query: str, category: str, limit: int) -> Dict[str, Any]:
        """搜索知识库实现"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 构建搜索SQL
            sql = '''
                SELECT ke.title, ke.content, ke.category, ke.tags, 
                       ud.filename, ud.upload_time
                FROM knowledge_entries ke
                JOIN uploaded_documents ud ON ke.document_id = ud.id
                WHERE ke.content LIKE ?
            '''
            params = [f'%{query}%']
            
            if category:
                sql += ' AND ke.category = ?'
                params.append(category)
            
            sql += ' ORDER BY ke.confidence_score DESC LIMIT ?'
            params.append(limit)
            
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            search_results = []
            for result in results:
                search_results.append({
                    "标题": result[0],
                    "内容": result[1][:200] + "..." if len(result[1]) > 200 else result[1],
                    "分类": result[2],
                    "标签": result[3],
                    "来源文件": result[4],
                    "上传时间": result[5]
                })
            
            return {
                "查询": query,
                "结果数量": len(search_results),
                "结果": search_results
            }
            
        finally:
            conn.close()

    def _analyze_divination_impl(self, question: str, hexagram_number: int, changing_lines: List[int]) -> Dict[str, Any]:
        """占卜分析实现"""
        # 获取主卦信息
        hexagram_info = self._query_hexagram_impl(str(hexagram_number), "number")
        
        if "错误" in hexagram_info:
            return hexagram_info
        
        analysis = {
            "问题": question,
            "主卦": hexagram_info,
            "分析时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "基本解读": self._generate_basic_interpretation(hexagram_info, question),
            "时运分析": self._analyze_timing(hexagram_info),
            "行动建议": self._generate_advice(hexagram_info, question)
        }
        
        if changing_lines:
            analysis["变爻分析"] = self._analyze_changing_lines(hexagram_number, changing_lines)
        
        return analysis

    def _generate_basic_interpretation(self, hexagram_info: Dict, question: str) -> str:
        """生成基本解读"""
        return f"针对您关于'{question}'的问题，{hexagram_info['卦名']}卦显示：{hexagram_info['解释']}"

    def _analyze_timing(self, hexagram_info: Dict) -> str:
        """分析时运"""
        return f"从{hexagram_info['卦名']}卦的角度看，当前时机需要{hexagram_info.get('现代应用', '谨慎把握')}"

    def _generate_advice(self, hexagram_info: Dict, question: str) -> List[str]:
        """生成建议"""
        return [
            f"根据{hexagram_info['卦名']}卦的指导，建议保持{hexagram_info['象辞'][:10]}的态度",
            "注重内在修养，顺应自然规律",
            "在行动前多思考，把握好时机"
        ]

    def _analyze_changing_lines(self, hexagram_number: int, changing_lines: List[int]) -> Dict[str, Any]:
        """分析变爻"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT id FROM hexagrams WHERE number = ?', (hexagram_number,))
            hexagram_id = cursor.fetchone()[0]
            
            changing_analysis = []
            for line_num in changing_lines:
                cursor.execute('''
                    SELECT line_text, interpretation 
                    FROM yao_lines 
                    WHERE hexagram_id = ? AND line_number = ?
                ''', (hexagram_id, line_num))
                
                result = cursor.fetchone()
                if result:
                    changing_analysis.append({
                        "爻位": f"第{line_num}爻",
                        "爻辞": result[0],
                        "解释": result[1],
                        "变化意义": f"第{line_num}爻的变化表示情况将有所转变"
                    })
            
            return {
                "变爻详情": changing_analysis,
                "整体变化": f"共有{len(changing_lines)}个变爻，表示变化较为明显",
                "应对策略": "关注变化趋势，适时调整策略"
            }
            
        finally:
            conn.close()

def main():
    """主函数"""
    server_instance = YijingKnowledgeServer()
    
    try:
        # 如果有mcp库，使用标准MCP服务器
        import mcp.server.stdio
        mcp.server.stdio.run_server(server_instance.server)
    except ImportError:
        # 简单的测试模式
        print("MCP库未安装，运行测试模式")
        print("可用工具:", list(server_instance.server.tools.keys()))

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
可用的易经MCP服务器
基于现有的SimpleYijingServer，符合OpenHands MCP协议
"""

import asyncio
import json
import sys
import os
from typing import Any, Sequence

# 导入现有的易经服务器
from .simple_yijing_server import SimpleYijingServer

# 使用标准MCP库
try:
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        Resource,
        Tool,
        TextContent,
        ImageContent,
        EmbeddedResource,
    )
except ImportError:
    print("错误：需要安装MCP库")
    print("请运行：pip install mcp")
    sys.exit(1)

# 创建服务器实例
server = Server("yijing-knowledge")

# 创建易经服务器实例
yijing_server = SimpleYijingServer()

@server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """
    列出可用的易经工具
    """
    return [
        Tool(
            name="yijing_hexagram_query",
            description="查询易经六十四卦信息 - 支持按卦名、卦号或情况查询",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "查询内容（卦名、卦号或相关情况）",
                    },
                    "method": {
                        "type": "string",
                        "enum": ["name", "number", "situation"],
                        "description": "查询方式：name(按卦名)、number(按卦号)、situation(按情况)",
                        "default": "name"
                    },
                },
                "required": ["query"],
            },
        ),
        Tool(
            name="yijing_divination_analysis",
            description="易经占卜分析 - 基于问题和卦象提供人生指导",
            inputSchema={
                "type": "object",
                "properties": {
                    "question": {
                        "type": "string",
                        "description": "要占卜的问题",
                    },
                    "hexagram_number": {
                        "type": "integer",
                        "minimum": 1,
                        "maximum": 64,
                        "description": "卦象编号（1-64）",
                    },
                },
                "required": ["question", "hexagram_number"],
            },
        ),
        Tool(
            name="yijing_knowledge_search",
            description="搜索易经知识库 - 在上传的文档和卦象中搜索相关内容",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "搜索关键词",
                    },
                    "limit": {
                        "type": "integer",
                        "minimum": 1,
                        "maximum": 50,
                        "description": "返回结果数量限制",
                        "default": 10
                    },
                },
                "required": ["query"],
            },
        ),
        Tool(
            name="yijing_upload_knowledge",
            description="上传易经相关知识文档到知识库",
            inputSchema={
                "type": "object",
                "properties": {
                    "filename": {
                        "type": "string",
                        "description": "文件名",
                    },
                    "content": {
                        "type": "string",
                        "description": "文档内容",
                    },
                    "category": {
                        "type": "string",
                        "description": "文档分类",
                        "default": "general"
                    },
                    "description": {
                        "type": "string",
                        "description": "文档描述",
                        "default": ""
                    },
                },
                "required": ["filename", "content"],
            },
        ),
        Tool(
            name="yijing_get_statistics",
            description="获取易经知识库统计信息",
            inputSchema={
                "type": "object",
                "properties": {},
            },
        ),
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict[str, Any]) -> list[TextContent]:
    """
    处理工具调用
    """
    try:
        if name == "yijing_hexagram_query":
            query = arguments.get("query", "")
            method = arguments.get("method", "name")
            
            result = yijing_server.query_hexagram(query, method)
            
            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, ensure_ascii=False, indent=2),
                )
            ]
        
        elif name == "yijing_divination_analysis":
            question = arguments.get("question", "")
            hexagram_number = arguments.get("hexagram_number", 1)
            
            result = yijing_server.divination_analysis(question, hexagram_number)
            
            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, ensure_ascii=False, indent=2),
                )
            ]
        
        elif name == "yijing_knowledge_search":
            query = arguments.get("query", "")
            limit = arguments.get("limit", 10)
            
            result = yijing_server.search_knowledge(query, limit)
            
            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, ensure_ascii=False, indent=2),
                )
            ]
        
        elif name == "yijing_upload_knowledge":
            filename = arguments.get("filename", "")
            content = arguments.get("content", "")
            category = arguments.get("category", "general")
            description = arguments.get("description", "")
            
            result = yijing_server.upload_knowledge(filename, content, category, description)
            
            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, ensure_ascii=False, indent=2),
                )
            ]
        
        elif name == "yijing_get_statistics":
            result = yijing_server.get_statistics()
            
            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, ensure_ascii=False, indent=2),
                )
            ]
        
        else:
            raise ValueError(f"未知工具：{name}")
    
    except Exception as e:
        return [
            TextContent(
                type="text",
                text=json.dumps({
                    "状态": "错误",
                    "错误": str(e),
                    "工具": name,
                    "参数": arguments
                }, ensure_ascii=False, indent=2),
            )
        ]

async def main():
    """
    主函数 - 启动MCP服务器
    """
    # 使用stdio传输
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())

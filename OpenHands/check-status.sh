#!/bin/bash

echo "=== OpenHands 状态检查 ==="
echo "时间: $(date)"
echo ""

echo "1. 检查端口状态:"
netstat -tlnp | grep -E "(3000|3001)" || echo "没有发现3000或3001端口"
echo ""

echo "2. 检查进程状态:"
ps aux | grep -E "(uvicorn|node.*3001|sirv)" | grep -v grep || echo "没有发现相关进程"
echo ""

echo "3. 检查Docker容器:"
docker ps | grep openhands || echo "没有发现OpenHands容器"
echo ""

echo "4. 测试本地API:"
curl -s -I http://localhost:3000/api/health | head -3 || echo "API测试失败"
echo ""

echo "5. 测试前端:"
curl -s -I http://localhost:3001 | head -3 || echo "前端测试失败"
echo ""

echo "6. 检查配置文件:"
if [ -f "config.toml" ]; then
    echo "config.toml 存在:"
    cat config.toml
else
    echo "config.toml 不存在"
fi
echo ""

echo "7. 检查日志目录:"
if [ -d "logs" ]; then
    echo "日志目录存在，最新文件:"
    ls -la logs/ | tail -5
else
    echo "日志目录不存在"
fi

echo ""
echo "=== 检查完成 ==="

#!/bin/bash

# OpenHands自动化回滚系统
# 版本: 2.0
# 作者: 系统管理员
# 日期: 2025-08-04

set -e

# 配置变量
BACKUP_BASE_DIR="/backup/openhands"
PROJECT_DIR="/www/wwwroot/ai.guiyunai.fun"
LOG_FILE="/var/log/openhands_rollback.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

# 显示可用备份
list_backups() {
    echo "📋 可用备份列表:"
    echo "================"
    
    if [ ! -d "$BACKUP_BASE_DIR" ]; then
        error "备份目录不存在: $BACKUP_BASE_DIR"
        exit 1
    fi
    
    cd "$BACKUP_BASE_DIR"
    local backups=($(ls -1t | grep -E '^[0-9]{8}_[0-9]{6}$' | head -10))
    
    if [ ${#backups[@]} -eq 0 ]; then
        error "没有找到可用的备份"
        exit 1
    fi
    
    for i in "${!backups[@]}"; do
        local backup_dir="${backups[$i]}"
        local backup_path="$BACKUP_BASE_DIR/$backup_dir"
        local size=$(du -sh "$backup_path" 2>/dev/null | cut -f1)
        local date_formatted=$(echo "$backup_dir" | sed 's/_/ /' | sed 's/\([0-9]\{4\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)/\1-\2-\3/')
        
        echo "$((i+1)). $backup_dir ($date_formatted) - 大小: $size"
        
        # 显示备份内容摘要
        if [ -f "$backup_path/MANIFEST.txt" ]; then
            local file_count=$(grep -c "^/" "$backup_path/MANIFEST.txt" 2>/dev/null || echo "未知")
            echo "   文件数量: $file_count"
        fi
    done
    
    echo ""
    echo "最新备份: ${BACKUP_BASE_DIR}/latest"
}

# 验证备份完整性
verify_backup() {
    local backup_dir="$1"
    
    log "验证备份完整性: $backup_dir"
    
    # 检查必要文件
    local required_files=(
        "restore.sh"
        "MANIFEST.txt"
        "system_info.txt"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$backup_dir/$file" ]; then
            error "备份不完整，缺少文件: $file"
            return 1
        fi
    done
    
    # 检查数据文件
    local data_files=(
        "data/openhands_user_data.tar.gz"
        "config"
        "nginx"
        "docker"
    )
    
    for item in "${data_files[@]}"; do
        if [ ! -e "$backup_dir/$item" ]; then
            warning "备份中缺少: $item"
        fi
    done
    
    log "备份验证通过"
    return 0
}

# 创建回滚前备份
create_pre_rollback_backup() {
    log "创建回滚前备份..."
    
    local pre_backup_dir="${BACKUP_BASE_DIR}/pre_rollback_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$pre_backup_dir"
    
    # 快速备份当前关键配置
    if [ -f "${PROJECT_DIR}/OpenHands/config.toml" ]; then
        cp "${PROJECT_DIR}/OpenHands/config.toml" "$pre_backup_dir/"
    fi
    
    if [ -d "${PROJECT_DIR}/.openhands" ]; then
        tar -czf "$pre_backup_dir/current_openhands_data.tar.gz" -C "${PROJECT_DIR}" .openhands
    fi
    
    # 备份当前Nginx配置
    cp /etc/nginx/sites-available/ai.guiyunai.fun "$pre_backup_dir/" 2>/dev/null || true
    
    # 记录当前容器状态
    docker ps -a > "$pre_backup_dir/current_containers.txt"
    
    echo "$pre_backup_dir" > "${BACKUP_BASE_DIR}/.last_pre_rollback"
    log "回滚前备份完成: $pre_backup_dir"
}

# 停止服务
stop_services() {
    log "停止OpenHands服务..."
    
    # 停止OpenHands容器
    docker stop openhands-app 2>/dev/null || true
    
    # 停止运行时容器
    docker ps | grep runtime | awk '{print $1}' | xargs -r docker stop
    
    # 停止Nginx（可选，根据需要）
    if [ "$STOP_NGINX" = "true" ]; then
        systemctl stop nginx
    fi
    
    log "服务已停止"
}

# 执行回滚
perform_rollback() {
    local backup_dir="$1"
    
    log "开始回滚到: $backup_dir"
    
    # 验证备份
    if ! verify_backup "$backup_dir"; then
        error "备份验证失败，回滚中止"
        exit 1
    fi
    
    # 创建回滚前备份
    create_pre_rollback_backup
    
    # 停止服务
    stop_services
    
    # 执行恢复脚本
    log "执行恢复脚本..."
    cd "$backup_dir"
    
    if [ -f "restore.sh" ]; then
        bash restore.sh
    else
        error "恢复脚本不存在"
        exit 1
    fi
    
    log "回滚完成"
}

# 验证回滚结果
verify_rollback() {
    log "验证回滚结果..."
    
    # 等待服务启动
    sleep 30
    
    # 检查容器状态
    if docker ps | grep -q openhands-app; then
        log "✅ OpenHands容器正在运行"
    else
        error "❌ OpenHands容器未运行"
        return 1
    fi
    
    # 检查HTTP服务
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
    if [ "$http_code" = "200" ]; then
        log "✅ HTTP服务正常 (HTTP $http_code)"
    else
        error "❌ HTTP服务异常 (HTTP $http_code)"
        return 1
    fi
    
    # 检查域名访问
    local domain_code=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null)
    if [ "$domain_code" = "200" ]; then
        log "✅ 域名访问正常 (HTTP $domain_code)"
    else
        warning "⚠️ 域名访问异常 (HTTP $domain_code)"
    fi
    
    log "回滚验证完成"
    return 0
}

# 自动检测问题并回滚
auto_detect_and_rollback() {
    log "自动检测系统问题..."
    
    local issues=0
    
    # 检查OpenHands容器
    if ! docker ps | grep -q openhands-app; then
        error "检测到问题: OpenHands容器未运行"
        ((issues++))
    fi
    
    # 检查HTTP服务
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
    if [ "$http_code" != "200" ]; then
        error "检测到问题: HTTP服务异常 (HTTP $http_code)"
        ((issues++))
    fi
    
    # 检查Nginx配置
    if ! nginx -t >/dev/null 2>&1; then
        error "检测到问题: Nginx配置错误"
        ((issues++))
    fi
    
    if [ $issues -gt 0 ]; then
        warning "检测到 $issues 个问题，建议执行回滚"
        return 1
    else
        log "系统状态正常"
        return 0
    fi
}

# 交互式回滚
interactive_rollback() {
    echo "🔄 OpenHands交互式回滚系统"
    echo "=========================="
    
    list_backups
    
    echo "请选择要回滚的备份 (输入数字，或 'q' 退出):"
    read -p "> " choice
    
    if [ "$choice" = "q" ]; then
        echo "回滚已取消"
        exit 0
    fi
    
    # 获取备份列表
    cd "$BACKUP_BASE_DIR"
    local backups=($(ls -1t | grep -E '^[0-9]{8}_[0-9]{6}$' | head -10))
    
    if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le "${#backups[@]}" ]; then
        local selected_backup="${backups[$((choice-1))]}"
        local backup_path="$BACKUP_BASE_DIR/$selected_backup"
        
        echo ""
        echo "选择的备份: $selected_backup"
        echo "备份路径: $backup_path"
        echo ""
        
        read -p "确认回滚到此备份? (y/N): " confirm
        if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
            perform_rollback "$backup_path"
            verify_rollback
        else
            echo "回滚已取消"
        fi
    else
        error "无效选择"
        exit 1
    fi
}

# 快速回滚到最新备份
quick_rollback() {
    log "快速回滚到最新备份..."
    
    local latest_backup="${BACKUP_BASE_DIR}/latest"
    
    if [ ! -L "$latest_backup" ]; then
        error "最新备份链接不存在"
        exit 1
    fi
    
    local backup_path=$(readlink "$latest_backup")
    
    echo "回滚到最新备份: $backup_path"
    read -p "确认执行快速回滚? (y/N): " confirm
    
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        perform_rollback "$backup_path"
        verify_rollback
    else
        echo "回滚已取消"
    fi
}

# 显示帮助
show_help() {
    cat << EOF
OpenHands回滚系统使用说明
========================

用法: $0 [选项]

选项:
  -l, --list          列出可用备份
  -i, --interactive   交互式回滚
  -q, --quick         快速回滚到最新备份
  -a, --auto          自动检测问题并建议回滚
  -v, --verify PATH   验证指定备份的完整性
  -h, --help          显示此帮助信息

示例:
  $0 -l                    # 列出所有备份
  $0 -i                    # 交互式选择回滚
  $0 -q                    # 快速回滚到最新备份
  $0 -a                    # 自动检测系统问题
  $0 -v /backup/openhands/20250804_163000  # 验证备份

注意事项:
- 回滚前会自动创建当前状态的备份
- 回滚过程中会停止相关服务
- 建议在维护窗口期间执行回滚操作
EOF
}

# 主函数
main() {
    case "${1:-}" in
        -l|--list)
            list_backups
            ;;
        -i|--interactive)
            interactive_rollback
            ;;
        -q|--quick)
            quick_rollback
            ;;
        -a|--auto)
            if ! auto_detect_and_rollback; then
                echo ""
                echo "检测到系统问题，是否执行自动回滚到最新备份?"
                read -p "(y/N): " auto_confirm
                if [ "$auto_confirm" = "y" ] || [ "$auto_confirm" = "Y" ]; then
                    quick_rollback
                fi
            fi
            ;;
        -v|--verify)
            if [ -z "$2" ]; then
                error "请指定备份路径"
                exit 1
            fi
            verify_backup "$2"
            ;;
        -h|--help)
            show_help
            ;;
        "")
            echo "请指定操作选项，使用 -h 查看帮助"
            show_help
            ;;
        *)
            error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

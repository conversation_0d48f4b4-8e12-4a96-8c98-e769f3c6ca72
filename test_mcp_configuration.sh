#!/bin/bash

echo "🧪 测试OpenHands MCP配置"
echo "================================"

echo "📊 检查MCP代理服务器状态..."

# 检查文件系统服务器
echo -n "文件系统服务器 (8080): "
if curl -s -f http://localhost:8080/sse >/dev/null 2>&1; then
    echo "✅ 运行正常"
else
    echo "❌ 连接失败"
fi

# 检查内存服务器
echo -n "内存服务器 (8083): "
if curl -s -f http://localhost:8083/sse >/dev/null 2>&1; then
    echo "✅ 运行正常"
else
    echo "❌ 连接失败"
fi

echo ""
echo "🔍 检查OpenHands容器状态..."
docker ps | grep openhands-app

echo ""
echo "📋 检查OpenHands日志（最近20行）..."
docker logs openhands-app --tail 20

echo ""
echo "🌐 检查OpenHands Web界面..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200"; then
    echo "✅ OpenHands Web界面正常 (http://localhost:3000)"
else
    echo "❌ OpenHands Web界面异常"
fi

echo ""
echo "📝 MCP配置摘要:"
echo "- 文件系统服务器: http://localhost:8080/sse"
echo "- 内存服务器: http://localhost:8083/sse"
echo "- 配置文件: /www/wwwroot/ai.guiyunai.fun/OpenHands/config.toml"

echo ""
echo "🎯 测试建议:"
echo "1. 在OpenHands中创建新对话"
echo "2. 尝试使用文件操作命令"
echo "3. 测试内存功能"
echo "4. 检查是否有MCP工具可用"

echo ""
echo "🔧 如果遇到问题："
echo "- 检查代理服务器日志: ls -la /www/wwwroot/ai.guiyunai.fun/mcp-proxies/logs/"
echo "- 重启代理服务器: cd /www/wwwroot/ai.guiyunai.fun/mcp-proxies && ./start_basic_mcp.sh"
echo "- 重启OpenHands: docker restart openhands-app"

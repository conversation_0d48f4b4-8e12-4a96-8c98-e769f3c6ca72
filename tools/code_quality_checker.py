#!/usr/bin/env python3
"""
代码质量检查工具
基于OpenHands标准检查代码质量
"""

import ast
import os
import re
import sys
from typing import List, Dict, Any, Tuple
from pathlib import Path

class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self):
        self.issues = []
        self.stats = {
            "files_checked": 0,
            "total_lines": 0,
            "docstring_coverage": 0,
            "type_annotation_coverage": 0,
            "error_handling_score": 0
        }
    
    def check_file(self, file_path: str) -> Dict[str, Any]:
        """检查单个文件的代码质量"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 解析AST
            try:
                tree = ast.parse(content)
            except SyntaxError as e:
                issues.append({
                    "type": "syntax_error",
                    "line": e.lineno,
                    "message": f"语法错误: {e.msg}",
                    "severity": "error"
                })
                return {"file": file_path, "issues": issues}
            
            # 各项检查
            issues.extend(self._check_docstrings(tree, file_path))
            issues.extend(self._check_type_annotations(tree, file_path))
            issues.extend(self._check_error_handling(tree, file_path))
            issues.extend(self._check_imports(tree, file_path))
            issues.extend(self._check_naming_conventions(tree, file_path))
            issues.extend(self._check_code_style(lines, file_path))
            
            self.stats["files_checked"] += 1
            self.stats["total_lines"] += len(lines)
            
            return {"file": file_path, "issues": issues}
            
        except Exception as e:
            issues.append({
                "type": "file_error",
                "line": 0,
                "message": f"文件读取错误: {e}",
                "severity": "error"
            })
            return {"file": file_path, "issues": issues}
    
    def _check_docstrings(self, tree: ast.AST, file_path: str) -> List[Dict[str, Any]]:
        """检查文档字符串"""
        issues = []
        
        class DocstringChecker(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # 检查函数文档字符串
                if not ast.get_docstring(node):
                    # 跳过私有方法和测试方法
                    if not node.name.startswith('_') and not node.name.startswith('test_'):
                        issues.append({
                            "type": "missing_docstring",
                            "line": node.lineno,
                            "message": f"函数 '{node.name}' 缺少文档字符串",
                            "severity": "warning"
                        })
                else:
                    # 检查文档字符串格式
                    docstring = ast.get_docstring(node)
                    if not self._is_google_style_docstring(docstring):
                        issues.append({
                            "type": "docstring_format",
                            "line": node.lineno,
                            "message": f"函数 '{node.name}' 的文档字符串不符合Google风格",
                            "severity": "info"
                        })
                
                self.generic_visit(node)
            
            def visit_ClassDef(self, node):
                # 检查类文档字符串
                if not ast.get_docstring(node):
                    issues.append({
                        "type": "missing_docstring",
                        "line": node.lineno,
                        "message": f"类 '{node.name}' 缺少文档字符串",
                        "severity": "warning"
                    })
                
                self.generic_visit(node)
            
            def _is_google_style_docstring(self, docstring: str) -> bool:
                """检查是否为Google风格的文档字符串"""
                if not docstring:
                    return False
                
                # 简单检查：包含Args或Returns部分
                google_sections = ['Args:', 'Returns:', 'Raises:', 'Yields:', 'Note:', 'Example:']
                return any(section in docstring for section in google_sections)
        
        checker = DocstringChecker()
        checker.visit(tree)
        
        return issues
    
    def _check_type_annotations(self, tree: ast.AST, file_path: str) -> List[Dict[str, Any]]:
        """检查类型注解"""
        issues = []
        
        class TypeAnnotationChecker(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # 检查函数参数类型注解
                for arg in node.args.args:
                    if not arg.annotation and arg.arg != 'self':
                        issues.append({
                            "type": "missing_type_annotation",
                            "line": node.lineno,
                            "message": f"函数 '{node.name}' 的参数 '{arg.arg}' 缺少类型注解",
                            "severity": "info"
                        })
                
                # 检查返回值类型注解
                if not node.returns and not node.name.startswith('_'):
                    issues.append({
                        "type": "missing_return_annotation",
                        "line": node.lineno,
                        "message": f"函数 '{node.name}' 缺少返回值类型注解",
                        "severity": "info"
                    })
                
                self.generic_visit(node)
        
        checker = TypeAnnotationChecker()
        checker.visit(tree)
        
        return issues
    
    def _check_error_handling(self, tree: ast.AST, file_path: str) -> List[Dict[str, Any]]:
        """检查错误处理"""
        issues = []
        
        class ErrorHandlingChecker(ast.NodeVisitor):
            def visit_Try(self, node):
                # 检查是否有裸露的except
                for handler in node.handlers:
                    if handler.type is None:
                        issues.append({
                            "type": "bare_except",
                            "line": handler.lineno,
                            "message": "使用了裸露的except子句，应该指定具体的异常类型",
                            "severity": "warning"
                        })
                    
                    # 检查是否有空的except块
                    if len(handler.body) == 1 and isinstance(handler.body[0], ast.Pass):
                        issues.append({
                            "type": "empty_except",
                            "line": handler.lineno,
                            "message": "空的except块，应该添加适当的错误处理",
                            "severity": "warning"
                        })
                
                self.generic_visit(node)
            
            def visit_Raise(self, node):
                # 检查是否重新抛出异常时保留了原始异常信息
                if node.exc is None:  # bare raise
                    # 这是好的做法，重新抛出当前异常
                    pass
                
                self.generic_visit(node)
        
        checker = ErrorHandlingChecker()
        checker.visit(tree)
        
        return issues
    
    def _check_imports(self, tree: ast.AST, file_path: str) -> List[Dict[str, Any]]:
        """检查导入语句"""
        issues = []
        
        class ImportChecker(ast.NodeVisitor):
            def visit_Import(self, node):
                for alias in node.names:
                    # 检查是否使用了相对导入
                    if alias.name.startswith('.'):
                        issues.append({
                            "type": "relative_import",
                            "line": node.lineno,
                            "message": f"使用了相对导入: {alias.name}",
                            "severity": "info"
                        })
                
                self.generic_visit(node)
            
            def visit_ImportFrom(self, node):
                # 检查是否从__future__导入
                if node.module == '__future__':
                    # 这是好的做法
                    pass
                
                self.generic_visit(node)
        
        checker = ImportChecker()
        checker.visit(tree)
        
        return issues
    
    def _check_naming_conventions(self, tree: ast.AST, file_path: str) -> List[Dict[str, Any]]:
        """检查命名规范"""
        issues = []
        
        class NamingChecker(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # 检查函数命名（应该是snake_case）
                if not re.match(r'^[a-z_][a-z0-9_]*$', node.name) and not node.name.startswith('test_'):
                    issues.append({
                        "type": "naming_convention",
                        "line": node.lineno,
                        "message": f"函数名 '{node.name}' 不符合snake_case命名规范",
                        "severity": "info"
                    })
                
                self.generic_visit(node)
            
            def visit_ClassDef(self, node):
                # 检查类命名（应该是PascalCase）
                if not re.match(r'^[A-Z][a-zA-Z0-9]*$', node.name):
                    issues.append({
                        "type": "naming_convention",
                        "line": node.lineno,
                        "message": f"类名 '{node.name}' 不符合PascalCase命名规范",
                        "severity": "info"
                    })
                
                self.generic_visit(node)
            
            def visit_Name(self, node):
                # 检查常量命名（应该是UPPER_CASE）
                if isinstance(node.ctx, ast.Store) and node.id.isupper():
                    if not re.match(r'^[A-Z_][A-Z0-9_]*$', node.id):
                        issues.append({
                            "type": "naming_convention",
                            "line": node.lineno,
                            "message": f"常量名 '{node.id}' 不符合UPPER_CASE命名规范",
                            "severity": "info"
                        })
                
                self.generic_visit(node)
        
        checker = NamingChecker()
        checker.visit(tree)
        
        return issues
    
    def _check_code_style(self, lines: List[str], file_path: str) -> List[Dict[str, Any]]:
        """检查代码风格"""
        issues = []
        
        for i, line in enumerate(lines, 1):
            # 检查行长度
            if len(line) > 88:  # Black的默认行长度
                issues.append({
                    "type": "line_too_long",
                    "line": i,
                    "message": f"行长度超过88字符: {len(line)}",
                    "severity": "info"
                })
            
            # 检查尾随空格
            if line.rstrip() != line and line.strip():
                issues.append({
                    "type": "trailing_whitespace",
                    "line": i,
                    "message": "行末有多余的空格",
                    "severity": "info"
                })
            
            # 检查制表符
            if '\t' in line:
                issues.append({
                    "type": "tab_character",
                    "line": i,
                    "message": "使用了制表符，应该使用空格",
                    "severity": "warning"
                })
        
        return issues
    
    def check_directory(self, directory: str) -> Dict[str, Any]:
        """检查整个目录"""
        results = []
        
        for root, dirs, files in os.walk(directory):
            # 跳过特定目录
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache', 'node_modules']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    result = self.check_file(file_path)
                    results.append(result)
                    self.issues.extend(result["issues"])
        
        return {
            "results": results,
            "summary": self._generate_summary()
        }
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成检查摘要"""
        issue_counts = {}
        severity_counts = {"error": 0, "warning": 0, "info": 0}
        
        for issue in self.issues:
            issue_type = issue["type"]
            severity = issue["severity"]
            
            issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1
            severity_counts[severity] += 1
        
        return {
            "total_issues": len(self.issues),
            "issue_counts": issue_counts,
            "severity_counts": severity_counts,
            "stats": self.stats
        }
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """生成检查报告"""
        report = []
        report.append("=" * 60)
        report.append("代码质量检查报告")
        report.append("=" * 60)
        
        summary = results["summary"]
        
        # 总体统计
        report.append(f"\n📊 总体统计:")
        report.append(f"  检查文件数: {self.stats['files_checked']}")
        report.append(f"  总行数: {self.stats['total_lines']}")
        report.append(f"  总问题数: {summary['total_issues']}")
        
        # 严重程度统计
        report.append(f"\n🚨 问题严重程度:")
        for severity, count in summary["severity_counts"].items():
            emoji = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}[severity]
            report.append(f"  {emoji} {severity}: {count}")
        
        # 问题类型统计
        if summary["issue_counts"]:
            report.append(f"\n📋 问题类型统计:")
            for issue_type, count in sorted(summary["issue_counts"].items()):
                report.append(f"  {issue_type}: {count}")
        
        # 详细问题列表
        if self.issues:
            report.append(f"\n🔍 详细问题列表:")
            current_file = None
            
            for result in results["results"]:
                if result["issues"]:
                    file_path = result["file"]
                    if file_path != current_file:
                        report.append(f"\n📁 {file_path}:")
                        current_file = file_path
                    
                    for issue in result["issues"]:
                        emoji = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}[issue["severity"]]
                        report.append(f"  {emoji} 行{issue['line']}: {issue['message']}")
        
        # 建议
        report.append(f"\n💡 改进建议:")
        if summary["severity_counts"]["error"] > 0:
            report.append("  - 优先修复所有错误级别的问题")
        if summary["severity_counts"]["warning"] > 0:
            report.append("  - 考虑修复警告级别的问题以提高代码质量")
        if summary["issue_counts"].get("missing_docstring", 0) > 0:
            report.append("  - 为公共函数和类添加文档字符串")
        if summary["issue_counts"].get("missing_type_annotation", 0) > 0:
            report.append("  - 添加类型注解以提高代码可读性")
        
        report.append("\n" + "=" * 60)
        
        return "\n".join(report)


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python code_quality_checker.py <目录或文件路径>")
        sys.exit(1)
    
    path = sys.argv[1]
    checker = CodeQualityChecker()
    
    if os.path.isfile(path):
        result = checker.check_file(path)
        results = {"results": [result], "summary": checker._generate_summary()}
    elif os.path.isdir(path):
        results = checker.check_directory(path)
    else:
        print(f"错误: 路径 '{path}' 不存在")
        sys.exit(1)
    
    # 生成并打印报告
    report = checker.generate_report(results)
    print(report)
    
    # 返回适当的退出码
    if results["summary"]["severity_counts"]["error"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()

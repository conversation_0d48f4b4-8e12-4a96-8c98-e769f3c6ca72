#!/usr/bin/env python3
"""
全面助理MCP服务器启动脚本
支持单独启动或批量启动所有MCP服务器
"""

import subprocess
import sys
import time
import os
import signal
from typing import List, Dict, Any
import argparse

class MCPServerManager:
    """MCP服务器管理器"""
    
    def __init__(self):
        self.servers = {
            "design": {
                "name": "设计创意服务器",
                "module": "mcp_servers.design_creative_server",
                "port": 8080,
                "description": "提供图形设计、UI/UX设计、品牌设计等创意服务"
            },
            "programming": {
                "name": "编程开发服务器", 
                "module": "mcp_servers.programming_dev_server",
                "port": 8081,
                "description": "提供代码生成、项目管理、版本控制等开发服务"
            },
            "marketing": {
                "name": "营销推广服务器",
                "module": "mcp_servers.marketing_promotion_server", 
                "port": 8082,
                "description": "提供中国媒体营销、社交媒体管理、内容创作等营销服务"
            },
            "script": {
                "name": "剧本杀服务器",
                "module": "mcp_servers.script_murder_server",
                "port": 8083,
                "description": "提供剧本创作、角色设计、游戏机制等剧本杀服务"
            },
            "fengshui": {
                "name": "风水分析服务器",
                "module": "mcp_servers.fengshui_server",
                "port": 8084,
                "description": "提供专业风水分析和建议服务"
            },
            "yijing": {
                "name": "易经占卜服务器",
                "module": "mcp_servers.yijing_server", 
                "port": 8085,
                "description": "提供易经占卜和哲学指导服务"
            }
        }
        self.processes = {}
        
    def start_server(self, server_key: str, use_proxy: bool = True) -> bool:
        """启动单个MCP服务器"""
        if server_key not in self.servers:
            print(f"❌ 未知的服务器: {server_key}")
            return False
            
        server_info = self.servers[server_key]
        print(f"🚀 启动 {server_info['name']}...")
        
        try:
            if use_proxy:
                # 使用SuperGateway代理启动
                cmd = [
                    "supergateway",
                    "--stdio", f"python -m {server_info['module']}",
                    "--port", str(server_info['port'])
                ]
                print(f"   代理地址: http://localhost:{server_info['port']}/sse")
            else:
                # 直接启动stdio服务器
                cmd = ["python", "-m", server_info['module']]
                print(f"   直接启动stdio服务器")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes[server_key] = process
            print(f"✅ {server_info['name']} 启动成功 (PID: {process.pid})")
            return True
            
        except Exception as e:
            print(f"❌ {server_info['name']} 启动失败: {e}")
            return False
    
    def start_all_servers(self, use_proxy: bool = True) -> None:
        """启动所有MCP服务器"""
        print("🌟 启动全面助理MCP服务器集群...")
        print("=" * 60)
        
        success_count = 0
        for server_key in self.servers:
            if self.start_server(server_key, use_proxy):
                success_count += 1
            time.sleep(1)  # 避免端口冲突
        
        print("=" * 60)
        print(f"📊 启动完成: {success_count}/{len(self.servers)} 个服务器成功启动")
        
        if use_proxy:
            print("\n🔗 代理服务器地址:")
            for key, info in self.servers.items():
                if key in self.processes:
                    print(f"   {info['name']}: http://localhost:{info['port']}/sse")
        
        print("\n💡 使用说明:")
        print("   1. 将上述地址添加到OpenHands的config.toml中的sse_servers配置")
        print("   2. 或者使用stdio_servers配置直接连接")
        print("   3. 按Ctrl+C停止所有服务器")
    
    def stop_all_servers(self) -> None:
        """停止所有MCP服务器"""
        print("\n🛑 正在停止所有MCP服务器...")
        
        for server_key, process in self.processes.items():
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ {self.servers[server_key]['name']} 已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"⚠️  {self.servers[server_key]['name']} 强制停止")
            except Exception as e:
                print(f"❌ 停止 {self.servers[server_key]['name']} 时出错: {e}")
        
        self.processes.clear()
        print("🏁 所有服务器已停止")
    
    def list_servers(self) -> None:
        """列出所有可用的MCP服务器"""
        print("📋 可用的MCP服务器:")
        print("=" * 80)
        
        for key, info in self.servers.items():
            status = "🟢 运行中" if key in self.processes else "⚪ 未启动"
            print(f"{status} {key:12} | {info['name']:20} | {info['description']}")
        
        print("=" * 80)
    
    def check_dependencies(self) -> bool:
        """检查依赖项"""
        print("🔍 检查依赖项...")
        
        # 检查Python模块
        required_modules = ["mcp.server.fastmcp"]
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError:
                print(f"❌ {module} - 请安装: pip install mcp")
                return False
        
        # 检查SuperGateway（如果使用代理）
        try:
            result = subprocess.run(["supergateway", "--version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ supergateway")
            else:
                print("⚠️  supergateway 未安装 - 将使用stdio模式")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("⚠️  supergateway 未安装 - 将使用stdio模式")
        
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="全面助理MCP服务器管理器")
    parser.add_argument("action", choices=["start", "start-all", "list", "check"], 
                       help="操作类型")
    parser.add_argument("--server", "-s", help="指定服务器名称（用于start操作）")
    parser.add_argument("--no-proxy", action="store_true", 
                       help="不使用代理，直接启动stdio服务器")
    
    args = parser.parse_args()
    
    manager = MCPServerManager()
    
    if args.action == "check":
        manager.check_dependencies()
        return
    
    if args.action == "list":
        manager.list_servers()
        return
    
    if args.action == "start":
        if not args.server:
            print("❌ 请指定服务器名称，使用 --server 参数")
            manager.list_servers()
            return
        
        if not manager.check_dependencies():
            return
        
        use_proxy = not args.no_proxy
        manager.start_server(args.server, use_proxy)
        
        try:
            print("\n按Ctrl+C停止服务器...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            manager.stop_all_servers()
    
    elif args.action == "start-all":
        if not manager.check_dependencies():
            return
        
        use_proxy = not args.no_proxy
        manager.start_all_servers(use_proxy)
        
        try:
            print("\n按Ctrl+C停止所有服务器...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            manager.stop_all_servers()

if __name__ == "__main__":
    main()

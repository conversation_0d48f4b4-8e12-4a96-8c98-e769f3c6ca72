#!/bin/bash

# OpenHands职业发展类MCP服务器代理启动脚本
# 严格遵循官方开发指南，使用SuperGateway代理方式
# 版本: 1.0
# 日期: 2025-08-04

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="${SCRIPT_DIR}/logs"
PID_DIR="${SCRIPT_DIR}/pids"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 启动MCP代理服务器
start_mcp_proxy() {
    local name=$1
    local command=$2
    local port=$3
    local description=$4
    
    log "启动 $name MCP代理服务器 (端口: $port)"
    
    # 检查端口是否已被占用
    if check_port $port; then
        warning "$name 代理服务器端口 $port 已被占用，跳过启动"
        return 0
    fi
    
    # 启动代理服务器
    nohup supergateway --stdio "$command" --port $port \
        > "$LOG_DIR/${name}_proxy.log" 2>&1 &
    
    local pid=$!
    echo $pid > "$PID_DIR/${name}_proxy.pid"
    
    # 等待服务启动
    sleep 5
    
    # 验证服务是否启动成功
    if check_port $port; then
        log "✅ $name MCP代理服务器启动成功 (PID: $pid, 端口: $port)"
        info "   描述: $description"
        info "   SSE端点: http://localhost:$port/sse"
        return 0
    else
        error "❌ $name MCP代理服务器启动失败"
        return 1
    fi
}

# 停止所有代理服务器
stop_all_proxies() {
    log "停止所有职业发展类MCP代理服务器..."
    
    for pid_file in "$PID_DIR"/*.pid; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            local name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid"
                log "已停止 $name (PID: $pid)"
            fi
            rm -f "$pid_file"
        fi
    done
}

# 显示运行状态
show_status() {
    log "职业发展类MCP代理服务器运行状态:"
    echo "================================"
    
    for pid_file in "$PID_DIR"/*.pid; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            local name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "✅ $name: ${GREEN}运行中${NC} (PID: $pid)"
            else
                echo -e "❌ $name: ${RED}已停止${NC}"
                rm -f "$pid_file"
            fi
        fi
    done
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            log "🚀 启动职业发展类MCP代理服务器集群"
            echo "=================================="
            
            # 职业发展类MCP服务器（使用现有的自定义服务器）
            start_mcp_proxy "programming-dev" "python3 /www/wwwroot/ai.guiyunai.fun/mcp_servers/programming_dev_server.py" 8110 "编程开发和技术学习"
            
            # GitHub项目发现服务器（模拟实现）
            start_mcp_proxy "github-discovery" "python3 -c \"
import asyncio
from mcp.server import Server
from mcp.types import Tool, TextContent
import json
import random

server = Server('github-discovery')

@server.list_tools()
async def list_tools():
    return [
        Tool(
            name='discover_projects',
            description='发现GitHub开源项目',
            inputSchema={
                'type': 'object',
                'properties': {
                    'language': {'type': 'string', 'description': '编程语言'},
                    'topic': {'type': 'string', 'description': '项目主题'},
                    'difficulty': {'type': 'string', 'description': '难度级别'}
                }
            }
        ),
        Tool(
            name='analyze_project',
            description='分析项目技术栈和贡献机会',
            inputSchema={
                'type': 'object',
                'properties': {
                    'repo_url': {'type': 'string', 'description': '仓库URL'},
                    'focus': {'type': 'string', 'description': '分析重点'}
                }
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: dict):
    if name == 'discover_projects':
        language = arguments.get('language', 'Python')
        topic = arguments.get('topic', 'AI')
        difficulty = arguments.get('difficulty', '中等')
        
        projects = f'发现了5个{language}语言的{topic}相关项目，难度级别：{difficulty}'
        return [TextContent(type='text', text=projects)]
    elif name == 'analyze_project':
        repo_url = arguments.get('repo_url', '')
        focus = arguments.get('focus', '技术栈')
        
        analysis = f'项目分析：{repo_url}，重点关注：{focus}'
        return [TextContent(type='text', text=analysis)]
    
if __name__ == '__main__':
    asyncio.run(server.run())
\"" 8111 "GitHub项目发现和分析"
            
            # 职位搜索服务器（模拟实现）
            start_mcp_proxy "job-search" "python3 -c \"
import asyncio
from mcp.server import Server
from mcp.types import Tool, TextContent
import json

server = Server('job-search')

@server.list_tools()
async def list_tools():
    return [
        Tool(
            name='search_jobs',
            description='搜索职位信息',
            inputSchema={
                'type': 'object',
                'properties': {
                    'keywords': {'type': 'string', 'description': '关键词'},
                    'location': {'type': 'string', 'description': '工作地点'},
                    'remote': {'type': 'boolean', 'description': '是否远程工作'}
                }
            }
        ),
        Tool(
            name='analyze_salary',
            description='分析薪资水平',
            inputSchema={
                'type': 'object',
                'properties': {
                    'position': {'type': 'string', 'description': '职位名称'},
                    'experience': {'type': 'string', 'description': '经验年限'}
                }
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: dict):
    if name == 'search_jobs':
        keywords = arguments.get('keywords', '')
        location = arguments.get('location', '全球')
        remote = arguments.get('remote', True)
        
        jobs = f'找到了{keywords}相关职位，地点：{location}，远程工作：{remote}'
        return [TextContent(type='text', text=jobs)]
    elif name == 'analyze_salary':
        position = arguments.get('position', '')
        experience = arguments.get('experience', '0-2年')
        
        salary = f'{position}职位，{experience}经验的薪资分析'
        return [TextContent(type='text', text=salary)]
    
if __name__ == '__main__':
    asyncio.run(server.run())
\"" 8112 "职位搜索和薪资分析"
            
            echo ""
            log "🎉 职业发展类MCP代理服务器集群启动完成"
            show_status
            ;;
            
        stop)
            stop_all_proxies
            ;;
            
        status)
            show_status
            ;;
            
        restart)
            stop_all_proxies
            sleep 2
            main start
            ;;
            
        *)
            echo "用法: $0 {start|stop|status|restart}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动所有职业发展类MCP代理服务器"
            echo "  stop    - 停止所有职业发展类MCP代理服务器"
            echo "  status  - 显示运行状态"
            echo "  restart - 重启所有代理服务器"
            exit 1
            ;;
    esac
}

# 信号处理
trap 'stop_all_proxies; exit 0' SIGINT SIGTERM

# 执行主函数
main "$@"

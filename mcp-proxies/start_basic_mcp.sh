#!/bin/bash

echo "🚀 启动基础MCP代理服务器..."

# 停止现有进程
pkill -f supergateway 2>/dev/null || true
sleep 2

# 创建日志目录
mkdir -p logs

# 1. 文件系统服务器代理 - 端口8080
echo "启动文件系统服务器代理 (端口8080)..."
nohup npx -y supergateway \
    --stdio "npx -y @modelcontextprotocol/server-filesystem /www/wwwroot/ai.guiyunai.fun/workspace" \
    --port 8080 \
    --ssePath /sse \
    --messagePath /message \
    --logLevel info > logs/filesystem.log 2>&1 &

# 2. 获取服务器代理 - 端口8081  
echo "启动获取服务器代理 (端口8081)..."
nohup npx -y supergateway \
    --stdio "npx -y @modelcontextprotocol/server-fetch" \
    --port 8081 \
    --ssePath /sse \
    --messagePath /message \
    --logLevel info > logs/fetch.log 2>&1 &

# 3. 内存服务器代理 - 端口8083
echo "启动内存服务器代理 (端口8083)..."
nohup npx -y supergateway \
    --stdio "npx -y @modelcontextprotocol/server-memory" \
    --port 8083 \
    --ssePath /sse \
    --messagePath /message \
    --logLevel info > logs/memory.log 2>&1 &

echo "等待服务器启动..."
sleep 10

echo ""
echo "🔍 检查服务器状态..."
for port in 8080 8081 8083; do
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:$port/sse | grep -q "200\|404"; then
        echo "✅ 端口 $port: 服务正常"
    else
        echo "❌ 端口 $port: 服务异常"
    fi
done

echo ""
echo "📊 进程状态:"
ps aux | grep supergateway | grep -v grep

echo ""
echo "🎯 MCP代理服务器已启动！"
echo "文件系统: http://localhost:8080/sse"
echo "获取服务: http://localhost:8081/sse" 
echo "内存服务: http://localhost:8083/sse"
echo ""
echo "📋 日志文件:"
echo "- 文件系统: logs/filesystem.log"
echo "- 获取服务: logs/fetch.log"
echo "- 内存服务: logs/memory.log"

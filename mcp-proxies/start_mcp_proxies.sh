#!/bin/bash

echo "启动MCP代理服务器..."

# 文件系统服务器代理 - 端口8080
echo "启动文件系统服务器代理 (端口8080)..."
npx -y supergateway \
    --stdio "npx -y @modelcontextprotocol/server-filesystem /www/wwwroot/ai.guiyunai.fun/workspace" \
    --port 8080 \
    --ssePath /sse \
    --messagePath /message \
    --logLevel info &

# 获取服务器代理 - 端口8081  
echo "启动获取服务器代理 (端口8081)..."
npx -y supergateway \
    --stdio "npx -y @modelcontextprotocol/server-fetch" \
    --port 8081 \
    --ssePath /sse \
    --messagePath /message \
    --logLevel info &

# 时间服务器代理 - 端口8082
echo "启动时间服务器代理 (端口8082)..."
npx -y supergateway \
    --stdio "npx -y @modelcontextprotocol/server-time" \
    --port 8082 \
    --ssePath /sse \
    --messagePath /message \
    --logLevel info &

# 内存服务器代理 - 端口8083
echo "启动内存服务器代理 (端口8083)..."
npx -y supergateway \
    --stdio "npx -y @modelcontextprotocol/server-memory" \
    --port 8083 \
    --ssePath /sse \
    --messagePath /message \
    --logLevel info &

echo "等待服务器启动..."
sleep 5

echo "检查服务器状态..."
for port in 8080 8081 8082 8083; do
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:$port/sse | grep -q "200\|404"; then
        echo "✅ 端口 $port: 服务正常"
    else
        echo "❌ 端口 $port: 服务异常"
    fi
done

echo ""
echo "🎯 MCP代理服务器已启动！"
echo "文件系统: http://localhost:8080/sse"
echo "获取服务: http://localhost:8081/sse" 
echo "时间服务: http://localhost:8082/sse"
echo "内存服务: http://localhost:8083/sse"

#!/bin/bash

# OpenHands创作类MCP服务器代理启动脚本
# 严格遵循官方开发指南，使用SuperGateway代理方式
# 版本: 1.0
# 日期: 2025-08-04

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="${SCRIPT_DIR}/logs"
PID_DIR="${SCRIPT_DIR}/pids"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 启动MCP代理服务器
start_mcp_proxy() {
    local name=$1
    local command=$2
    local port=$3
    local description=$4
    
    log "启动 $name MCP代理服务器 (端口: $port)"
    
    # 检查端口是否已被占用
    if check_port $port; then
        warning "$name 代理服务器端口 $port 已被占用，跳过启动"
        return 0
    fi
    
    # 启动代理服务器
    nohup supergateway --stdio "$command" --port $port \
        > "$LOG_DIR/${name}_proxy.log" 2>&1 &
    
    local pid=$!
    echo $pid > "$PID_DIR/${name}_proxy.pid"
    
    # 等待服务启动
    sleep 5
    
    # 验证服务是否启动成功
    if check_port $port; then
        log "✅ $name MCP代理服务器启动成功 (PID: $pid, 端口: $port)"
        info "   描述: $description"
        info "   SSE端点: http://localhost:$port/sse"
        return 0
    else
        error "❌ $name MCP代理服务器启动失败"
        return 1
    fi
}

# 停止所有代理服务器
stop_all_proxies() {
    log "停止所有创作类MCP代理服务器..."
    
    for pid_file in "$PID_DIR"/*.pid; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            local name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid"
                log "已停止 $name (PID: $pid)"
            fi
            rm -f "$pid_file"
        fi
    done
}

# 显示运行状态
show_status() {
    log "创作类MCP代理服务器运行状态:"
    echo "================================"
    
    for pid_file in "$PID_DIR"/*.pid; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            local name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "✅ $name: ${GREEN}运行中${NC} (PID: $pid)"
            else
                echo -e "❌ $name: ${RED}已停止${NC}"
                rm -f "$pid_file"
            fi
        fi
    done
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            log "🚀 启动创作类MCP代理服务器集群"
            echo "=================================="
            
            # 创作类MCP服务器（使用现有的自定义服务器）
            start_mcp_proxy "script-murder" "python3 /www/wwwroot/ai.guiyunai.fun/mcp_servers/script_murder_server.py" 8100 "剧本杀剧本创作和角色设计"
            start_mcp_proxy "design-creative" "python3 /www/wwwroot/ai.guiyunai.fun/mcp_servers/design_creative_server.py" 8101 "设计创意和视觉内容创作"
            
            # 故事生成服务器（模拟实现）
            start_mcp_proxy "story-generator" "python3 -c \"
import asyncio
from mcp.server import Server
from mcp.types import Tool, TextContent
import json
import random

server = Server('story-generator')

@server.list_tools()
async def list_tools():
    return [
        Tool(
            name='generate_story',
            description='生成故事情节和角色',
            inputSchema={
                'type': 'object',
                'properties': {
                    'genre': {'type': 'string', 'description': '故事类型'},
                    'characters': {'type': 'integer', 'description': '角色数量'},
                    'setting': {'type': 'string', 'description': '故事背景'}
                }
            }
        ),
        Tool(
            name='create_character',
            description='创建角色档案',
            inputSchema={
                'type': 'object',
                'properties': {
                    'name': {'type': 'string', 'description': '角色姓名'},
                    'role': {'type': 'string', 'description': '角色职业'},
                    'personality': {'type': 'string', 'description': '性格特点'}
                }
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: dict):
    if name == 'generate_story':
        genre = arguments.get('genre', '悬疑')
        characters = arguments.get('characters', 5)
        setting = arguments.get('setting', '现代都市')
        
        story = f'在{setting}背景下的{genre}故事，涉及{characters}个主要角色...'
        return [TextContent(type='text', text=story)]
    elif name == 'create_character':
        name = arguments.get('name', '未知')
        role = arguments.get('role', '普通人')
        personality = arguments.get('personality', '神秘')
        
        character = f'角色档案：{name}，职业：{role}，性格：{personality}'
        return [TextContent(type='text', text=character)]
    
if __name__ == '__main__':
    asyncio.run(server.run())
\"" 8102 "AI故事生成和情节构建"
            
            echo ""
            log "🎉 创作类MCP代理服务器集群启动完成"
            show_status
            ;;
            
        stop)
            stop_all_proxies
            ;;
            
        status)
            show_status
            ;;
            
        restart)
            stop_all_proxies
            sleep 2
            main start
            ;;
            
        *)
            echo "用法: $0 {start|stop|status|restart}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动所有创作类MCP代理服务器"
            echo "  stop    - 停止所有创作类MCP代理服务器"
            echo "  status  - 显示运行状态"
            echo "  restart - 重启所有代理服务器"
            exit 1
            ;;
    esac
}

# 信号处理
trap 'stop_all_proxies; exit 0' SIGINT SIGTERM

# 执行主函数
main "$@"

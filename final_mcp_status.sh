#!/bin/bash

echo "🎯 OpenHands MCP最终状态报告"
echo "================================"

echo "📊 版本信息:"
echo "✅ 您使用的是最新版本 OpenHands 0.51"
echo "✅ 此版本包含完整的MCP支持"

echo ""
echo "🐳 容器状态:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | head -1
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep openhands-app || echo "❌ openhands-app 容器未运行"

echo ""
echo "🔧 MCP代理服务器状态:"
if curl -s -f http://localhost:8080/sse >/dev/null 2>&1; then
    echo "✅ 文件系统服务器 (8080): 运行正常"
else
    echo "❌ 文件系统服务器 (8080): 连接失败"
fi

if curl -s -f http://localhost:8083/sse >/dev/null 2>&1; then
    echo "✅ 内存服务器 (8083): 运行正常"
else
    echo "❌ 内存服务器 (8083): 连接失败"
fi

echo ""
echo "🌐 Web界面状态:"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ OpenHands Web界面正常 (http://localhost:3000)"
else
    echo "❌ OpenHands Web界面异常 (HTTP: $HTTP_CODE)"
fi

echo ""
echo "📝 MCP配置:"
echo "配置文件: /www/wwwroot/ai.guiyunai.fun/OpenHands/config.toml"
echo "MCP服务器:"
echo "  - 文件系统: http://localhost:8080/sse"
echo "  - 内存服务: http://localhost:8083/sse"

echo ""
echo "🎯 下一步操作:"
echo "1. 访问 OpenHands: http://localhost:3000"
echo "2. 清除浏览器缓存 (Ctrl+Shift+R)"
echo "3. 进入设置页面"
echo "4. 查找 'MCP' 选项卡"

echo ""
echo "❓ 如果仍然看不到MCP选项:"
echo "1. 检查浏览器开发者工具控制台是否有错误"
echo "2. 确认您在设置页面的正确位置"
echo "3. MCP选项可能在 '高级设置' 或 '集成' 部分"
echo "4. 尝试创建新对话，MCP工具可能直接在对话中可用"

echo ""
echo "🔧 管理命令:"
echo "./mcp_manager.sh status   # 检查MCP状态"
echo "./mcp_manager.sh restart  # 重启MCP服务"
echo "docker logs openhands-app # 查看OpenHands日志"

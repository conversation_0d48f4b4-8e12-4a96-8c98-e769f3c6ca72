#!/bin/bash

echo "⚡ 系统级性能优化"
echo "================================"

# 1. Docker优化配置
echo "🐳 优化Docker配置..."

# 创建Docker daemon配置
sudo mkdir -p /etc/docker
cat << 'EOF' | sudo tee /etc/docker/daemon.json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "default-runtime": "runc",
  "live-restore": true,
  "max-concurrent-downloads": 10,
  "max-concurrent-uploads": 5,
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ]
}
EOF

# 2. 系统内核参数优化
echo "🔧 优化系统内核参数..."
cat << 'EOF' | sudo tee -a /etc/sysctl.conf
# OpenHands性能优化
vm.max_map_count=262144
fs.file-max=65536
net.core.somaxconn=65535
net.ipv4.tcp_max_syn_backlog=65535
net.core.netdev_max_backlog=5000
EOF

# 3. 用户限制优化
echo "👤 优化用户限制..."
cat << 'EOF' | sudo tee -a /etc/security/limits.conf
# OpenHands优化
* soft nofile 65536
* hard nofile 65536
* soft nproc 65536
* hard nproc 65536
EOF

# 4. 清理系统缓存
echo "🧹 清理系统缓存..."
sudo sync
echo 3 | sudo tee /proc/sys/vm/drop_caches

# 5. Docker清理
echo "🗑️ 清理Docker资源..."
docker system prune -f
docker volume prune -f

# 6. 预热常用镜像
echo "🔥 预热常用镜像..."
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.51-nikolaik &
docker pull docker.all-hands.dev/all-hands-ai/openhands:0.51 &
wait

echo ""
echo "✅ 系统优化完成！"
echo ""
echo "🔄 需要重启的服务："
echo "sudo systemctl restart docker"
echo "cd /www/wwwroot/ai.guiyunai.fun/OpenHands && docker-compose restart"

echo ""
echo "📊 验证优化效果："
echo "docker info | grep -E '(Storage Driver|Logging Driver)'"
echo "docker run --rm hello-world  # 测试启动速度"

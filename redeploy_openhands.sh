#!/bin/bash

echo "🚀 OpenHands官方重新部署脚本"
echo "================================"

echo "📋 部署配置:"
echo "- 镜像: docker.all-hands.dev/all-hands-ai/openhands:0.51"
echo "- LLM模型: groq/llama-3.3-70b-versatile"
echo "- 端口: 3000"
echo "- Workspace: /www/wwwroot/ai.guiyunai.fun/workspace"

echo ""
echo "🔍 步骤1: 验证环境准备..."

# 检查Docker服务
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker服务未运行"
    exit 1
fi
echo "✅ Docker服务正常"

# 检查workspace目录
if [ ! -d "/www/wwwroot/ai.guiyunai.fun/workspace" ]; then
    echo "创建workspace目录..."
    mkdir -p /www/wwwroot/ai.guiyunai.fun/workspace
fi
echo "✅ Workspace目录: $(ls -la /www/wwwroot/ai.guiyunai.fun/workspace | wc -l) 个文件"

# 检查端口3000是否可用
if netstat -tlnp | grep :3000 >/dev/null 2>&1; then
    echo "⚠️ 端口3000被占用，尝试清理..."
    # 这里不强制杀死，因为可能是其他重要服务
else
    echo "✅ 端口3000可用"
fi

echo ""
echo "📥 步骤2: 拉取最新镜像..."
docker pull docker.all-hands.dev/all-hands-ai/openhands:0.51

echo ""
echo "🚀 步骤3: 启动OpenHands容器..."

# 使用官方推荐的部署方式
docker run -d \
    --name openhands-app \
    --restart unless-stopped \
    -p 3000:3000 \
    -v /www/wwwroot/ai.guiyunai.fun/workspace:/workspace \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -e SANDBOX_USER_ID=1000 \
    -e LLM_MODEL="groq/llama-3.3-70b-versatile" \
    -e LLM_API_KEY="********************************************************" \
    -e LLM_BASE_URL="https://api.groq.com/openai/v1" \
    -e WORKSPACE_BASE="/workspace" \
    -e RUNTIME="docker" \
    docker.all-hands.dev/all-hands-ai/openhands:0.51

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功"
else
    echo "❌ 容器启动失败"
    exit 1
fi

echo ""
echo "⏳ 步骤4: 等待服务启动..."
echo "等待60秒让服务完全启动..."

for i in {1..12}; do
    echo "等待中... ($i/12)"
    sleep 5
    
    # 检查容器是否还在运行
    if ! docker ps | grep openhands-app >/dev/null; then
        echo "❌ 容器意外停止，查看日志:"
        docker logs openhands-app --tail 20
        exit 1
    fi
done

echo ""
echo "🔍 步骤5: 验证部署状态..."

# 检查容器状态
echo "容器状态:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep openhands-app

# 检查服务响应
echo ""
echo "服务响应测试:"
for i in {1..6}; do
    echo "测试 $i/6..."
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ HTTP响应正常 (200)"
        break
    elif [ "$HTTP_CODE" = "000" ]; then
        echo "⏳ 服务还在启动中..."
    else
        echo "⚠️ HTTP响应: $HTTP_CODE"
    fi
    
    if [ $i -eq 6 ]; then
        echo "❌ 服务响应异常，查看日志:"
        docker logs openhands-app --tail 30
    else
        sleep 10
    fi
done

echo ""
echo "🎯 部署完成状态报告:"
echo "================================"

# 最终状态检查
if docker ps | grep openhands-app >/dev/null; then
    echo "✅ 容器运行状态: 正常"
else
    echo "❌ 容器运行状态: 异常"
fi

HTTP_FINAL=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
if [ "$HTTP_FINAL" = "200" ]; then
    echo "✅ Web服务状态: 正常 (HTTP $HTTP_FINAL)"
    echo "🌐 本地访问: http://localhost:3000"
    echo "🌐 外部访问: https://ai.guiyunai.fun"
else
    echo "❌ Web服务状态: 异常 (HTTP $HTTP_FINAL)"
fi

echo ""
echo "📋 下一步操作:"
echo "1. 访问 http://localhost:3000 验证界面"
echo "2. 配置MCP服务器集成"
echo "3. 测试AI对话功能"
